#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取t3-data-lake-std-bak XML中的目录名
"""

import xml.etree.ElementTree as ET
import urllib.parse

def extract_directories_from_xml(xml_content):
    """从XML内容中提取目录名"""
    try:
        # 解析XML
        root = ET.fromstring(xml_content)
        
        # 定义命名空间
        namespace = {'ns': 'http://obs.myhwclouds.com/doc/2015-06-30/'}
        
        # 查找所有CommonPrefixes元素
        common_prefixes = root.findall('.//ns:CommonPrefixes/ns:Prefix', namespace)
        
        directories = []
        for prefix in common_prefixes:
            if prefix.text:
                # URL解码
                decoded = urllib.parse.unquote(prefix.text)
                # 移除末尾的斜杠
                directory_name = decoded.rstrip('/')
                directories.append(directory_name)
        
        return sorted(directories)
    
    except ET.ParseError as e:
        print(f"XML解析错误: {e}")
        return []
    except Exception as e:
        print(f"处理错误: {e}")
        return []

def main():
    # t3-data-lake-std-bak XML内容
    xml_content = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?><ListBucketResult xmlns="http://obs.myhwclouds.com/doc/2015-06-30/"><Name>t3-data-lake-std-bak</Name><Prefix></Prefix><Marker></Marker><MaxKeys>500</MaxKeys><Delimiter>%2F</Delimiter><IsTruncated>false</IsTruncated><EncodingType>url</EncodingType><CommonPrefixes><Prefix>data%2F</Prefix><MTime>1687230449</MTime><Mode>16895</Mode><InodeNo>4611851810798305536</InodeNo><LastModified>2023-06-20T03:07:29.000Z</LastModified></CommonPrefixes><CommonPrefixes><Prefix>datalake%2F</Prefix><MTime>1701077270</MTime><Mode>16832</Mode><InodeNo>4611954535137413632</InodeNo><LastModified>2023-11-27T09:27:50.000Z</LastModified></CommonPrefixes><CommonPrefixes><Prefix>hubble%2F</Prefix><MTime>1720612258</MTime><Mode>16832</Mode><InodeNo>4611920052684329728</InodeNo><LastModified>2024-07-10T11:50:58.000Z</LastModified></CommonPrefixes><CommonPrefixes><Prefix>k8s%2F</Prefix><MTime>1671170712</MTime><Mode>16895</Mode><Gid>0</Gid><Uid>0</Uid><InodeNo>4611894240497172736</InodeNo><LastModified>2022-12-16T06:05:12.000Z</LastModified></CommonPrefixes><CommonPrefixes><Prefix>negative-manage-voice%2F</Prefix><MTime>1750045936</MTime><Mode>16832</Mode><InodeNo>4611812781563249664</InodeNo><LastModified>2025-06-16T03:52:16.000Z</LastModified></CommonPrefixes><CommonPrefixes><Prefix>negative-webapp-api%2F</Prefix><MTime>1750728245</MTime><Mode>16832</Mode><InodeNo>4611922680696480000</InodeNo><LastModified>2025-06-24T01:24:05.000Z</LastModified></CommonPrefixes><CommonPrefixes><Prefix>partner-workorder-web%2F</Prefix><MTime>1755680978</MTime><Mode>16832</Mode><InodeNo>4611962586559613440</InodeNo><LastModified>2025-08-20T09:09:38.000Z</LastModified></CommonPrefixes><CommonPrefixes><Prefix>sec-driving%2F</Prefix><MTime>1747727643</MTime><Mode>16832</Mode><InodeNo>4611964859437948928</InodeNo><LastModified>2025-05-20T07:54:03.000Z</LastModified></CommonPrefixes><CommonPrefixes><Prefix>t3op-admin-api%2F</Prefix><MTime>1756137608</MTime><Mode>16832</Mode><InodeNo>4611912746502194688</InodeNo><LastModified>2025-08-25T16:00:08.000Z</LastModified></CommonPrefixes><CommonPrefixes><Prefix>tmp%2F</Prefix><MTime>1692870032</MTime><Mode>16832</Mode><InodeNo>4611888854870918912</InodeNo><LastModified>2023-08-24T09:40:32.000Z</LastModified></CommonPrefixes><CommonPrefixes><Prefix>trash%2F</Prefix><MTime>1678445167</MTime><Mode>16832</Mode><InodeNo>4611905208104518400</InodeNo><LastModified>2023-03-10T10:46:07.000Z</LastModified></CommonPrefixes><CommonPrefixes><Prefix>user%2F</Prefix><MTime>1699256206</MTime><Mode>16832</Mode><InodeNo>4611773948890972416</InodeNo><LastModified>2023-11-06T07:36:46.000Z</LastModified></CommonPrefixes><CommonPrefixes><Prefix>var%2F</Prefix><MTime>1671094453</MTime><Mode>16877</Mode><Gid>0</Gid><Uid>0</Uid><InodeNo>4611893222539395328</InodeNo><LastModified>2022-12-15T08:54:13.000Z</LastModified></CommonPrefixes><CommonPrefixes><Prefix>workspace%2F</Prefix><MTime>1679047687</MTime><Mode>16832</Mode><InodeNo>4611830404860739840</InodeNo><LastModified>2023-03-17T10:08:07.000Z</LastModified></CommonPrefixes></ListBucketResult>'''
    
    # 提取目录名
    directories = extract_directories_from_xml(xml_content)
    
    print("t3-data-lake-std-bak 目录列表:")
    print("=" * 50)
    for i, directory in enumerate(directories, 1):
        print(f"{i:2d}. {directory}")
    
    print(f"\n总共找到 {len(directories)} 个目录")
    
    # 保存到文件
    with open('datalake_directories.txt', 'w', encoding='utf-8') as f:
        for directory in directories:
            f.write(f"{directory}\n")
    
    print("\n目录名已保存到 datalake_directories.txt 文件中")
    
    # 生成表格格式
    generate_table_formats(directories)

def generate_table_formats(directories):
    """生成不同格式的表格"""
    
    # Markdown表格格式
    print("\n" + "="*60)
    print("Markdown表格格式:")
    print("="*60)
    
    markdown_table = "| 序号 | 目录名 | 类型 |\n"
    markdown_table += "|------|--------|------|\n"
    
    for i, directory in enumerate(directories, 1):
        # 简单分类
        if 'api' in directory:
            category = 'API服务'
        elif 'web' in directory:
            category = 'Web应用'
        elif 'data' in directory or 'datalake' in directory:
            category = '数据服务'
        elif 'k8s' in directory:
            category = 'K8s服务'
        elif 'voice' in directory:
            category = '语音服务'
        elif directory in ['tmp', 'trash', 'var', 'user', 'workspace']:
            category = '系统目录'
        elif 'hubble' in directory:
            category = '监控服务'
        else:
            category = '其他服务'
            
        markdown_table += f"| {i} | {directory} | {category} |\n"
    
    print(markdown_table)
    
    # 保存Markdown表格
    with open('datalake_directories_table.md', 'w', encoding='utf-8') as f:
        f.write("# t3-data-lake-std-bak 目录列表\n\n")
        f.write(markdown_table)
    
    print(f"\n文件已生成:")
    print("- datalake_directories.txt (目录列表)")
    print("- datalake_directories_table.md (Markdown表格)")

if __name__ == "__main__":
    main()
