#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将huawei.json内容转换为Excel表格
"""

import json
import pandas as pd
import os
from collections import defaultdict

def analyze_config_type(line_content):
    """分析配置类型"""
    line_lower = line_content.lower()
    
    if 'mysql' in line_lower or 'jdbc:mysql' in line_lower:
        return 'MySQL数据库'
    elif 'redis' in line_lower:
        return 'Redis缓存'
    elif 'obs' in line_lower and 'huaweicloud' in line_lower:
        return '华为云OBS'
    elif 'es.host' in line_lower or 'elasticsearch' in line_lower:
        return 'Elasticsearch'
    elif 'apig' in line_lower and 'huaweicloud' in line_lower:
        return '华为云API网关'
    elif 'myhuaweicloud.com' in line_lower:
        return '华为云服务'
    elif 'huaweicloud' in line_lower:
        return '华为云基础服务'
    elif 'huawei' in line_lower:
        return '华为相关配置'
    else:
        return '其他配置'

def convert_huawei_json_to_excel():
    """将华为配置JSON转换为Excel表格"""
    
    json_file = 'huawei.json'
    
    # 检查文件是否存在
    if not os.path.exists(json_file):
        print(f"错误: {json_file} 文件不存在")
        return None
    
    # 读取JSON文件
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None
    except Exception as e:
        print(f"读取文件错误: {e}")
        return None
    
    # 提取数据
    data_list = data.get("data", [])
    
    if not data_list:
        print("JSON文件中没有找到数据")
        return None
    
    # 为每条记录添加配置类型分析
    enhanced_data = []
    for item in data_list:
        enhanced_item = item.copy()
        enhanced_item['配置类型'] = analyze_config_type(item.get('line', ''))
        enhanced_data.append(enhanced_item)
    
    # 转换为DataFrame
    df = pd.DataFrame(enhanced_data)
    
    # 重新排列列的顺序
    columns_order = ['servicename', 'namespace', '配置类型', 'line']
    df = df[columns_order]
    
    # 生成Excel文件
    output_file = 'huawei_config_complete.xlsx'
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 主数据表
        df.to_excel(writer, sheet_name='华为云配置详情', index=False)
        
        # 服务统计表
        service_stats = df['servicename'].value_counts().reset_index()
        service_stats.columns = ['服务名', '配置数量']
        service_stats.to_excel(writer, sheet_name='服务统计', index=False)
        
        # 配置类型统计表
        config_type_stats = df['配置类型'].value_counts().reset_index()
        config_type_stats.columns = ['配置类型', '数量']
        config_type_stats.to_excel(writer, sheet_name='配置类型统计', index=False)
        
        # 命名空间统计表
        namespace_stats = df['namespace'].value_counts().reset_index()
        namespace_stats.columns = ['命名空间', '数量']
        namespace_stats.to_excel(writer, sheet_name='命名空间统计', index=False)
    
    print(f"Excel文件已生成: {output_file}")
    print(f"包含 {len(data_list)} 条记录")
    
    # 统计信息
    service_count = df['servicename'].nunique()
    config_type_count = df['配置类型'].nunique()
    namespace_count = df['namespace'].nunique()
    
    print(f"涉及 {service_count} 个服务")
    print(f"包含 {config_type_count} 种配置类型")
    print(f"涉及 {namespace_count} 个命名空间")
    
    # 显示服务统计（前15个）
    service_stats_dict = df['servicename'].value_counts()
    print(f"\n=== 配置最多的服务 (前15个) ===")
    for service, count in service_stats_dict.head(15).items():
        print(f"{service}: {count}条配置")
    
    # 显示配置类型统计
    config_type_stats_dict = df['配置类型'].value_counts()
    print(f"\n=== 配置类型统计 ===")
    for config_type, count in config_type_stats_dict.items():
        print(f"{config_type}: {count}条")
    
    # 显示表格预览
    print(f"\n=== 表格预览 (前5行) ===")
    print(df.head().to_string(index=False, max_colwidth=60))
    
    return df

def main():
    """主函数"""
    print("开始转换huawei.json为Excel表格...")
    df = convert_huawei_json_to_excel()
    
    if df is not None:
        print("\n转换完成！")
        print("\nExcel文件包含以下工作表:")
        print("1. 华为云配置详情 - 完整的配置数据")
        print("2. 服务统计 - 每个服务的配置数量")
        print("3. 配置类型统计 - 各种配置类型的数量")
        print("4. 命名空间统计 - 各个命名空间的配置数量")
        
        print("\n列说明:")
        print("- servicename: 服务名称")
        print("- namespace: 配置文件命名空间")
        print("- 配置类型: 自动识别的配置类型")
        print("- line: 配置行内容")

if __name__ == "__main__":
    main()
