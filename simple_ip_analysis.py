#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版IP配置分析，生成包含servicename、namespace、line的Excel文件
过滤掉127.0.0.1的配置，只保留在"所有服务列表.xls"中的服务
"""

import json
import pandas as pd
import re
import os

def load_services_from_excel():
    """从Excel文件中加载服务列表"""
    try:
        # 读取Excel文件
        df = pd.read_excel('所有服务列表.xls')
        
        # 获取所有服务名（假设服务名在第一列）
        services = set()
        for col in df.columns:
            for value in df[col].dropna():
                if isinstance(value, str) and value.strip():
                    services.add(value.strip())
        
        print(f"从Excel文件加载了 {len(services)} 个服务")
        return services
    
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return set()

def contains_non_localhost_ip(line_content):
    """检查配置行是否包含非127.0.0.1的IP地址"""
    # IP地址正则表达式
    ip_pattern = r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b'
    
    # 查找所有IP地址
    ips = re.findall(ip_pattern, line_content)
    
    # 检查是否有非localhost的IP
    for ip in ips:
        if not ip.startswith('127.') and ip != '0.0.0.0' and ip != '***************':
            return True
    
    return False

def analyze_ip_json():
    """分析ip.json文件"""
    # 加载服务列表
    all_services = load_services_from_excel()
    
    # 检查ip.json文件
    json_file = 'ip.json'
    if not os.path.exists(json_file) or os.path.getsize(json_file) == 0:
        print(f"错误: {json_file} 文件不存在或为空")
        return []
    
    # 读取ip.json文件
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return []
    except Exception as e:
        print(f"读取文件错误: {e}")
        return []
    
    # 分析数据
    results = []
    
    for item in data.get('data', []):
        service_name = item.get('servicename', '')
        namespace = item.get('namespace', '')
        line_content = item.get('line', '')
        
        # 只处理在服务列表中的服务
        if service_name in all_services:
            # 检查是否包含非127.0.0.1的IP地址
            if contains_non_localhost_ip(line_content):
                results.append({
                    'servicename': service_name,
                    'namespace': namespace,
                    'line': line_content
                })
    
    print(f"找到 {len(results)} 条需要处理的IP配置记录")
    return results

def generate_simple_excel(results):
    """生成简化的Excel文件"""
    if not results:
        print("没有找到需要处理的IP配置记录")
        return
    
    # 创建DataFrame
    df = pd.DataFrame(results)
    
    # 生成Excel文件
    output_file = 'ip_config_filtered.xlsx'
    df.to_excel(output_file, index=False, sheet_name='IP配置')
    
    print(f"Excel文件已生成: {output_file}")
    print(f"包含 {len(results)} 条记录")
    
    # 显示统计信息
    service_count = df['servicename'].nunique()
    print(f"涉及 {service_count} 个服务")
    
    # 显示前10个服务的记录数
    service_stats = df['servicename'].value_counts().head(10)
    print("\n=== 配置记录最多的服务 ===")
    for service, count in service_stats.items():
        print(f"{service}: {count}条记录")

def main():
    """主函数"""
    print("开始分析ip.json文件...")
    print("过滤条件: 排除127.0.0.1，只保留所有服务列表中的服务")
    
    try:
        results = analyze_ip_json()
        generate_simple_excel(results)
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
