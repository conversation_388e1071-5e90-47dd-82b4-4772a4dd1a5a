#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将upLoad.json内容转换为Excel表格
"""

import json
import pandas as pd

def convert_upload_json_to_excel():
    """将上传配置JSON转换为Excel表格"""
    
    # JSON数据
    json_data = {
        "code": 200,
        "data": [
            {
                "servicename": "iov-web-api",
                "namespace": "application.yml",
                "line": "行号:82    download-url: https://upload-pre.t3go.cn/file"
            },
            {
                "servicename": "route-event",
                "namespace": "application.yml",
                "line": "行号:75    upload-url: https://upload-pre.t3go.cn/file/upload/private?bucketName=iov-media-private"
            },
            {
                "servicename": "fault-handler",
                "namespace": "application.yml",
                "line": "行号:167    download-url: https://upload-pre.t3go.cn/file"
            },
            {
                "servicename": "fault-handler",
                "namespace": "application.yml",
                "line": "行号:168    hardware-download-url: https://upload-pre.t3go.cn/file/upload/algoGrapics"
            },
            {
                "servicename": "fault-handler",
                "namespace": "application.yml",
                "line": "行号:171    upload-url: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "fault-handler",
                "namespace": "application.yml",
                "line": "行号:172    video_up_url: https://upload-pre.t3go.cn/file/media/upload/private"
            },
            {
                "servicename": "iov-api-gateway",
                "namespace": "application.yml",
                "line": "行号:25    upload_url: https://upload-pre.t3go.cn/file/upload/private"
            },
            {
                "servicename": "iov-api-gateway",
                "namespace": "application.yml",
                "line": "行号:26    video_up_url: https://upload-pre.t3go.cn/file/media/upload/private"
            },
            {
                "servicename": "iov-business",
                "namespace": "application.yml",
                "line": "行号:98    url: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "iov-business",
                "namespace": "application.yml",
                "line": "行号:99    durl: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "smartva-openplatform",
                "namespace": "application.yml",
                "line": "行号:120    upload_url: https://upload-pre.t3go.cn/file/upload/private?bucketName=iov-media-private"
            },
            {
                "servicename": "iov-manage",
                "namespace": "application.yml",
                "line": "行号:113    url: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "iov-manage",
                "namespace": "application.yml",
                "line": "行号:114    durl: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "iov-manage",
                "namespace": "application.yml",
                "line": "行号:115    video_up_url: https://upload-pre.t3go.cn/file/media/upload/private?bucketName=iov-media-private"
            },
            {
                "servicename": "iov-manage",
                "namespace": "application.yml",
                "line": "行号:116    upload_url: https://upload-pre.t3go.cn/file/upload/private?bucketName=iov-media-private"
            },
            {
                "servicename": "integrated-message",
                "namespace": "application.yml",
                "line": "行号:500  uploadFileServerUrl: https://upload-pre.t3go.cn/file/upload/public"
            },
            {
                "servicename": "integrated-message",
                "namespace": "application.yml",
                "line": "行号:501  getFileUrlByUuidPath: https://upload-pre.t3go.cn/file/url/%s"
            },
            {
                "servicename": "integrated-message",
                "namespace": "application.yml",
                "line": "行号:502  uploadPrivateFileServerUrl: https://upload-pre.t3go.cn/file/upload/private"
            },
            {
                "servicename": "route-admin-api",
                "namespace": "application.yml",
                "line": "行号:60    uploadServer: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "route-admin-api",
                "namespace": "application.yml",
                "line": "行号:61    downLoadServer: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "passenger-web-api",
                "namespace": "application.yml",
                "line": "行号:31      https://upload-pre.t3go.cn"
            },
            {
                "servicename": "route-selection",
                "namespace": "application.yml",
                "line": "行号:22      external-upload: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "route-selection",
                "namespace": "application.yml",
                "line": "行号:23      external-download: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "passenger-app-api",
                "namespace": "application.yml",
                "line": "行号:195      uploadfileServer: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "partner-support-fp",
                "namespace": "application.yml",
                "line": "行号:155  ossFileServer: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "lbs-fence",
                "namespace": "application.yml",
                "line": "行号:186      serviceHost: https://upload-pre.t3go.cn/"
            },
            {
                "servicename": "gis-gateway-api",
                "namespace": "application.yml",
                "line": "行号:56      serviceHost: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "t3-cc-web-api",
                "namespace": "application.yml",
                "line": "行号:130        server: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "t3-cc-web-api",
                "namespace": "application.yml",
                "line": "行号:206    uploadUrl: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "t3-cc-web-api",
                "namespace": "application.yml",
                "line": "行号:235    uploadUrl: https://upload-pre.t3go.cn/file/media/upload/private"
            },
            {
                "servicename": "t3-pictor-api",
                "namespace": "application.yml",
                "line": "行号:7  uploadUrl: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "marketing-center-fp",
                "namespace": "application.yml",
                "line": "行号:271  uploadFileServerUrl: https://upload-pre.t3go.cn/file/upload/public"
            },
            {
                "servicename": "open-app-api",
                "namespace": "application.yml",
                "line": "行号:114      uploadfileServer: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "open-app-api",
                "namespace": "application.yml",
                "line": "行号:115      downloadfileServer: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "open-app-api",
                "namespace": "application.yml",
                "line": "行号:116      allowDownloadDomains: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "common-web-api",
                "namespace": "application.yml",
                "line": "行号:48  uploadfileServer: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "common-web-api",
                "namespace": "application.yml",
                "line": "行号:70  ossFileServer: https://upload-pre.t3go.cn/"
            },
            {
                "servicename": "marketing-web-api",
                "namespace": "application.yml",
                "line": "行号:69  uploadfileServer: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "marketing-web-api",
                "namespace": "application.yml",
                "line": "行号:70  downloadfileServer: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "official-website-web-api",
                "namespace": "application.yml",
                "line": "行号:41        server: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "open-data-collection-fp",
                "namespace": "application.yml",
                "line": "行号:323      host: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "marketing-interactive-server-starter",
                "namespace": "application.yml",
                "line": "行号:149    uploadfileServer: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "passenger-activity-api",
                "namespace": "application.yml",
                "line": "行号:266  uploadFileServerUrl: https://upload-pre.t3go.cn/file/upload/public"
            },
            {
                "servicename": "passenger-activity-api",
                "namespace": "application.yml",
                "line": "行号:324  ossFileServer: https://upload-pre.t3go.cn #https://upload-pre.t3go.cn"
            },
            {
                "servicename": "passenger-activity-api",
                "namespace": "application.yml",
                "line": "行号:325  httpOssFileServer: http://gateway.t3go.com.cn/file-service #https://upload-pre.t3go.cn"
            },
            {
                "servicename": "support-center-config",
                "namespace": "application.yml",
                "line": "行号:98    file: https://upload-pre.t3go.cn/"
            },
            {
                "servicename": "t3-driver-resume-web",
                "namespace": "application.yml",
                "line": "行号:8  downloadfileServer: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "t3-driver-resume-web",
                "namespace": "application.yml",
                "line": "行号:9  uploadfileServer: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "operation-center",
                "namespace": "application.yml",
                "line": "行号:185    uploadUrl: https://upload-pre.t3go.cn/file/upload/private"
            },
            {
                "servicename": "operation-center",
                "namespace": "application.yml",
                "line": "行号:186    downloadUrl: http://upload-pre.t3go.cn/file/download"
            },
            {
                "servicename": "operation-center",
                "namespace": "application.yml",
                "line": "行号:187    fileUrl: https://upload-pre.t3go.cn/file/url"
            },
            {
                "servicename": "vigen-web-api",
                "namespace": "application.yml",
                "line": "行号:66        server: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "vigen-web-api",
                "namespace": "application.yml",
                "line": "行号:68        server: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "driver-resume",
                "namespace": "application.yml",
                "line": "行号:211  downloadfileServer: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "driver-resume",
                "namespace": "application.yml",
                "line": "行号:212  uploadfileServer: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "resource-recruit-api",
                "namespace": "application.yml",
                "line": "行号:117  uploadfileServer: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "driver-core-app-api",
                "namespace": "application.yml",
                "line": "行号:175    upload: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "finance-api",
                "namespace": "application.yml",
                "line": "行号:48      url: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "sec-driving",
                "namespace": "application.yml",
                "line": "行号:160    file-url: https://upload-pre.t3go.cn/file/get/batchUrl"
            },
            {
                "servicename": "risk-store-service",
                "namespace": "application.yml",
                "line": "行号:169    fileUploadUrl: https://upload-pre.t3go.cn/"
            },
            {
                "servicename": "risk-control-anti-multi-api",
                "namespace": "application.yml",
                "line": "行号:37    fileUrl: https://upload-pre.t3go.cn/file/url/"
            },
            {
                "servicename": "partner-workorder-web",
                "namespace": "application.yml",
                "line": "行号:48      upload-url: https://upload-pre.t3go.cn/"
            },
            {
                "servicename": "partner-workorder-web",
                "namespace": "application.yml",
                "line": "行号:49      download-url: https://upload-pre.t3go.cn/"
            },
            {
                "servicename": "route-pretrial",
                "namespace": "application.yml",
                "line": "行号:352      upload-url: https://upload-pre.t3go.cn/"
            },
            {
                "servicename": "partner-workorder-api",
                "namespace": "application.yml",
                "line": "行号:89      upload-url: https://upload-pre.t3go.cn/"
            },
            {
                "servicename": "sp-asset-core",
                "namespace": "application.yml",
                "line": "行号:178      url: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "recruit-rpa-external",
                "namespace": "application.yml",
                "line": "行号:168    fileUrl: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "ccp-autodrive-operation-manage",
                "namespace": "application.yml",
                "line": "行号:190    uploadUrl: https://upload-pre.t3go.cn/file/media/upload/private"
            },
            {
                "servicename": "partner-workorder-fp",
                "namespace": "application.yml",
                "line": "行号:148      upload-url: https://upload-pre.t3go.cn/"
            },
            {
                "servicename": "partner-workorder-fp",
                "namespace": "application.yml",
                "line": "行号:149      download-url: https://upload-pre.t3go.cn/"
            },
            {
                "servicename": "lens",
                "namespace": "application",
                "line": "行号:3  downloadUrl: https://upload-pre.t3go.cn/file/download/"
            },
            {
                "servicename": "node-bff-resource",
                "namespace": "application.yml",
                "line": "行号:10    host: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "recruit-rpa",
                "namespace": "application.yml",
                "line": "行号:170    fileUrl: https://upload-pre.t3go.cn"
            },
            {
                "servicename": "negative-webapp-api",
                "namespace": "application.yml",
                "line": "行号:51      upload-url: https://upload-pre.t3go.cn/"
            },
            {
                "servicename": "negative-webapp-api",
                "namespace": "application.yml",
                "line": "行号:52      download-url: https://upload-pre.t3go.cn/"
            }
        ],
        "message": "成功"
    }
    
    # 提取数据
    data_list = json_data["data"]
    
    # 转换为DataFrame
    df = pd.DataFrame(data_list)
    
    # 生成Excel文件
    output_file = 'upload_config_table.xlsx'
    df.to_excel(output_file, index=False, sheet_name='上传配置')
    
    print(f"Excel文件已生成: {output_file}")
    print(f"包含 {len(data_list)} 条记录")
    
    # 统计信息
    service_count = df['servicename'].nunique()
    print(f"涉及 {service_count} 个服务")
    
    # 显示服务统计
    service_stats = df['servicename'].value_counts()
    print(f"\n=== 配置最多的服务 ===")
    for service, count in service_stats.head(10).items():
        print(f"{service}: {count}条配置")
    
    # 显示表格预览
    print(f"\n=== 表格预览 ===")
    print(df.head().to_string(index=False))
    
    return df

def main():
    """主函数"""
    print("开始转换上传配置JSON为Excel表格...")
    df = convert_upload_json_to_excel()
    print("\n转换完成！")

if __name__ == "__main__":
    main()
