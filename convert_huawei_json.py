#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将huawei.json内容转换为Excel表格
"""

import json
import pandas as pd
import os

def create_sample_data():
    """创建示例数据"""
    return {
        "code": 200,
        "data": [
            {
                "servicename": "biz-iov-alarm-state-calc",
                "namespace": "application",
                "line": "行号:2  es.host: vpcep-a295c2eb-819f-466a-8905-3a4725d13e92.cn-east-3.huaweicloud.com"
            },
            {
                "servicename": "biz-iov-alarm-state-calc",
                "namespace": "application",
                "line": "行号:10  redis.host: redis-e1a56997-3b14-4beb-9995-d99b918a9998.dcs.huaweicloud.com"
            },
            {
                "servicename": "fault-handler",
                "namespace": "application.yml",
                "line": "行号:191  ai_gelatinization_server: https://9e8ec404b9df4741a5db1bcbdcf6143c.apig.cn-east-3.huaweicloudapis.com/v1/infers/9fa3eb2f-c669-48b3-8c2c-0a4155bace0a"
            },
            {
                "servicename": "integrated-center",
                "namespace": "application.yml",
                "line": "行号:152  imageAuditUrl: https://moderation.cn-east-3.myhuaweicloud.com/v1.0/moderation/image"
            },
            {
                "servicename": "file-service",
                "namespace": "application.yml",
                "line": "行号:44  endpoint: obs.cn-east-3.myhuaweicloud.com"
            },
            {
                "servicename": "passenger-center",
                "namespace": "application.yml",
                "line": "行号:102  imageUrl: https://t3propublic.obs.cn-east-3.myhuaweicloud.com/images/png/20200306143627-19abc114db084c8b9a45f28750f16b42.png"
            },
            {
                "servicename": "t3-admin",
                "namespace": "application.yml",
                "line": "行号:36  endpoint: obs.cn-east-3.myhuaweicloud.com"
            },
            {
                "servicename": "mssp-web",
                "namespace": "application.yml",
                "line": "行号:10  url: ***************************************************************************************************************************************************************************************************************"
            },
            {
                "servicename": "bigdata-hubble",
                "namespace": "application.yml",
                "line": "行号:697  url: https://obs.cn-east-3.myhuaweicloud.com"
            },
            {
                "servicename": "common-app-api",
                "namespace": "application-extend.yml",
                "line": "行号:63  \"picUrlSelected\": \"https://t3propublic.obs.cn-east-3.myhuaweicloud.com:443/3e865214-d67f-4d31-af32-995e1b51ecdf.png\","
            }
        ]
    }

def convert_huawei_json_to_excel():
    """将华为配置JSON转换为Excel表格"""

    # 检查文件是否存在且不为空
    json_file = 'huawei.json'
    if not os.path.exists(json_file) or os.path.getsize(json_file) == 0:
        print(f"警告: {json_file} 文件不存在或为空，使用示例数据")
        # 使用示例数据
        data = create_sample_data()
    else:
        # 读取JSON文件
        try:
            # 先尝试UTF-8编码
            with open(json_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if not content.strip():
                    print("文件为空，使用示例数据")
                    data = create_sample_data()
                else:
                    data = json.loads(content)
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(json_file, 'r', encoding='utf-8-sig') as f:
                    data = json.load(f)
            except Exception as e2:
                print(f"编码错误: {e2}")
                return
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            # 显示文件前100个字符用于调试
            with open(json_file, 'rb') as f:
                raw_content = f.read(100)
                print(f"文件前100字节: {raw_content}")
            return
        except Exception as e:
            print(f"读取文件错误: {e}")
            return
    
    # 提取数据
    data_list = data.get("data", [])
    
    if not data_list:
        print("JSON文件中没有找到数据")
        return
    
    # 转换为DataFrame
    df = pd.DataFrame(data_list)
    
    # 生成Excel文件
    output_file = 'huawei_config_table.xlsx'
    df.to_excel(output_file, index=False, sheet_name='华为云配置')
    
    print(f"Excel文件已生成: {output_file}")
    print(f"包含 {len(data_list)} 条记录")
    
    # 统计信息
    service_count = df['servicename'].nunique()
    print(f"涉及 {service_count} 个服务")
    
    # 显示服务统计
    service_stats = df['servicename'].value_counts()
    print(f"\n=== 配置最多的服务 (前15个) ===")
    for service, count in service_stats.head(15).items():
        print(f"{service}: {count}条配置")
    
    # 分析配置类型
    config_types = {}
    for _, row in df.iterrows():
        line = row['line']
        if 'mysql' in line.lower():
            config_types['MySQL数据库'] = config_types.get('MySQL数据库', 0) + 1
        elif 'redis' in line.lower():
            config_types['Redis缓存'] = config_types.get('Redis缓存', 0) + 1
        elif 'obs' in line.lower():
            config_types['对象存储OBS'] = config_types.get('对象存储OBS', 0) + 1
        elif 'huaweicloud' in line.lower():
            config_types['华为云服务'] = config_types.get('华为云服务', 0) + 1
        elif 'es.host' in line.lower():
            config_types['Elasticsearch'] = config_types.get('Elasticsearch', 0) + 1
        elif 'apig' in line.lower():
            config_types['API网关'] = config_types.get('API网关', 0) + 1
        else:
            config_types['其他配置'] = config_types.get('其他配置', 0) + 1
    
    print(f"\n=== 配置类型统计 ===")
    for config_type, count in sorted(config_types.items(), key=lambda x: x[1], reverse=True):
        print(f"{config_type}: {count}条")
    
    # 显示表格预览
    print(f"\n=== 表格预览 (前5行) ===")
    print(df.head().to_string(index=False, max_colwidth=80))
    
    return df

def main():
    """主函数"""
    print("开始转换huawei.json为Excel表格...")
    df = convert_huawei_json_to_excel()
    if df is not None:
        print("\n转换完成！")
        print("Excel文件包含以下列:")
        print("- servicename: 服务名称")
        print("- namespace: 配置文件命名空间")
        print("- line: 配置行内容")

if __name__ == "__main__":
    main()
