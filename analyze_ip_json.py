#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析ip.json文件，提取IP配置信息并生成Excel文件
只保留在所有服务列表中的服务
"""

import json
import pandas as pd
import re
from collections import defaultdict

def load_all_services():
    """加载所有之前提取的服务列表"""
    all_services = set()
    
    # 从各个目录文件中读取服务名
    service_files = [
        'directories.txt',           # t3proprivate
        'iov_directories.txt',       # iov-media-private
        'driver_directories.txt',    # driver-operation-compliance-private
        'voice_directories.txt',     # driver-voice-private
        'datalake_directories.txt'   # t3-data-lake-std-bak
    ]
    
    for file_path in service_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    service = line.strip()
                    if service:
                        all_services.add(service)
        except FileNotFoundError:
            print(f"警告: 文件 {file_path} 未找到")
    
    return all_services

def extract_ip_addresses(line_content):
    """从配置行中提取IP地址"""
    # IP地址正则表达式
    ip_pattern = r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b'
    
    # 查找所有IP地址
    ips = re.findall(ip_pattern, line_content)
    
    # 过滤掉本地IP地址
    filtered_ips = []
    for ip in ips:
        if not (ip.startswith('127.') or ip.startswith('0.0.0.0') or ip == '***************'):
            filtered_ips.append(ip)
    
    return filtered_ips

def categorize_ip(ip):
    """根据IP地址分类"""
    if ip.startswith('10.'):
        return '内网IP'
    elif ip.startswith('172.'):
        octets = ip.split('.')
        if len(octets) >= 2 and 16 <= int(octets[1]) <= 31:
            return '内网IP'
        else:
            return '其他私有IP'
    elif ip.startswith('192.168.'):
        return '内网IP'
    else:
        return '公网IP'

def extract_config_type(line_content):
    """从配置行中提取配置类型"""
    line_lower = line_content.lower()
    
    if 'mysql' in line_lower or 'jdbc:mysql' in line_lower:
        return 'MySQL数据库'
    elif 'redis' in line_lower:
        return 'Redis缓存'
    elif 'kafka' in line_lower:
        return 'Kafka消息队列'
    elif 'zookeeper' in line_lower or ':2181' in line_content:
        return 'Zookeeper'
    elif 'hbase' in line_lower:
        return 'HBase'
    elif 'elasticsearch' in line_lower or 'es.host' in line_lower:
        return 'Elasticsearch'
    elif 'mongodb' in line_lower:
        return 'MongoDB'
    elif 'clickhouse' in line_lower:
        return 'ClickHouse'
    elif 'http://' in line_lower or 'https://' in line_lower:
        return 'HTTP服务'
    elif 'ldap' in line_lower:
        return 'LDAP服务'
    else:
        return '其他配置'

def analyze_ip_json():
    """分析ip.json文件"""
    # 加载所有服务列表
    all_services = load_all_services()
    print(f"加载了 {len(all_services)} 个服务")
    
    # 读取ip.json文件
    try:
        with open('ip.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except UnicodeDecodeError:
        # 尝试其他编码
        with open('ip.json', 'r', encoding='utf-8-sig') as f:
            data = json.load(f)
    except json.JSONDecodeError:
        # 尝试读取文件内容并检查
        with open('ip.json', 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"文件前100个字符: {repr(content[:100])}")
            raise
    
    # 分析数据
    results = []
    service_stats = defaultdict(int)
    ip_stats = defaultdict(int)
    
    for item in data['data']:
        service_name = item['servicename']
        namespace = item['namespace']
        line_content = item['line']
        
        # 只处理在服务列表中的服务
        if service_name in all_services:
            # 提取IP地址
            ips = extract_ip_addresses(line_content)
            
            if ips:  # 只处理包含IP地址的行
                config_type = extract_config_type(line_content)
                
                for ip in ips:
                    ip_category = categorize_ip(ip)
                    
                    results.append({
                        '服务名': service_name,
                        '命名空间': namespace,
                        'IP地址': ip,
                        'IP类型': ip_category,
                        '配置类型': config_type,
                        '配置行': line_content,
                        '行号': line_content.split()[0] if line_content.startswith('行号:') else ''
                    })
                    
                    service_stats[service_name] += 1
                    ip_stats[ip] += 1
    
    print(f"找到 {len(results)} 条IP配置记录")
    print(f"涉及 {len(service_stats)} 个服务")
    print(f"涉及 {len(ip_stats)} 个唯一IP地址")
    
    return results, service_stats, ip_stats, all_services

def generate_excel_report(results, service_stats, ip_stats, all_services):
    """生成Excel报告"""
    
    # 创建Excel写入器
    with pd.ExcelWriter('ip_analysis_report.xlsx', engine='openpyxl') as writer:
        
        # 1. 主要数据表
        df_main = pd.DataFrame(results)
        df_main.to_excel(writer, sheet_name='IP配置详情', index=False)
        
        # 2. 服务统计表
        df_service_stats = pd.DataFrame([
            {'服务名': service, 'IP配置数量': count}
            for service, count in sorted(service_stats.items(), key=lambda x: x[1], reverse=True)
        ])
        df_service_stats.to_excel(writer, sheet_name='服务统计', index=False)
        
        # 3. IP统计表
        df_ip_stats = pd.DataFrame([
            {'IP地址': ip, '使用次数': count, 'IP类型': categorize_ip(ip)}
            for ip, count in sorted(ip_stats.items(), key=lambda x: x[1], reverse=True)
        ])
        df_ip_stats.to_excel(writer, sheet_name='IP统计', index=False)
        
        # 4. IP类型汇总
        ip_type_summary = defaultdict(int)
        for result in results:
            ip_type_summary[result['IP类型']] += 1
        
        df_ip_type = pd.DataFrame([
            {'IP类型': ip_type, '数量': count}
            for ip_type, count in ip_type_summary.items()
        ])
        df_ip_type.to_excel(writer, sheet_name='IP类型汇总', index=False)
        
        # 5. 配置类型汇总
        config_type_summary = defaultdict(int)
        for result in results:
            config_type_summary[result['配置类型']] += 1
        
        df_config_type = pd.DataFrame([
            {'配置类型': config_type, '数量': count}
            for config_type, count in config_type_summary.items()
        ])
        df_config_type.to_excel(writer, sheet_name='配置类型汇总', index=False)
        
        # 6. 服务覆盖情况
        services_with_ip = set(service_stats.keys())
        services_without_ip = all_services - services_with_ip
        
        coverage_data = [
            {'类型': '有IP配置的服务', '数量': len(services_with_ip)},
            {'类型': '无IP配置的服务', '数量': len(services_without_ip)},
            {'类型': '总服务数', '数量': len(all_services)}
        ]
        
        df_coverage = pd.DataFrame(coverage_data)
        df_coverage.to_excel(writer, sheet_name='服务覆盖情况', index=False)
        
        # 7. 无IP配置的服务列表
        if services_without_ip:
            df_no_ip = pd.DataFrame([
                {'服务名': service}
                for service in sorted(services_without_ip)
            ])
            df_no_ip.to_excel(writer, sheet_name='无IP配置服务', index=False)
    
    print("Excel报告已生成: ip_analysis_report.xlsx")

def main():
    """主函数"""
    # 检查ip.json文件是否存在且不为空
    import os

    json_file = 'ip.json'
    if not os.path.exists(json_file) or os.path.getsize(json_file) == 0:
        print(f"警告: {json_file} 文件不存在或为空，使用示例文件 sample_ip.json")
        json_file = 'sample_ip.json'

    print(f"开始分析{json_file}文件...")

    try:
        results, service_stats, ip_stats, all_services = analyze_ip_json(json_file)
        generate_excel_report(results, service_stats, ip_stats, all_services)
        
        print("\n=== 分析摘要 ===")
        print(f"总服务数: {len(all_services)}")
        print(f"有IP配置的服务数: {len(service_stats)}")
        print(f"IP配置记录数: {len(results)}")
        print(f"唯一IP地址数: {len(ip_stats)}")
        
        # 显示前10个最常用的IP
        print("\n=== 最常用的IP地址 ===")
        for ip, count in sorted(ip_stats.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"{ip}: {count}次 ({categorize_ip(ip)})")
        
        # 显示前10个IP配置最多的服务
        print("\n=== IP配置最多的服务 ===")
        for service, count in sorted(service_stats.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"{service}: {count}个IP配置")
            
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
