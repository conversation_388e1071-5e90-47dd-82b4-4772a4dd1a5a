#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取driver-operation-compliance-private XML中的目录名
"""

import xml.etree.ElementTree as ET
import urllib.parse

def extract_directories_from_xml(xml_content):
    """从XML内容中提取目录名"""
    try:
        # 解析XML
        root = ET.fromstring(xml_content)
        
        # 定义命名空间
        namespace = {'ns': 'http://obs.myhwclouds.com/doc/2015-06-30/'}
        
        # 查找所有CommonPrefixes元素
        common_prefixes = root.findall('.//ns:CommonPrefixes/ns:Prefix', namespace)
        
        directories = []
        for prefix in common_prefixes:
            if prefix.text:
                # URL解码
                decoded = urllib.parse.unquote(prefix.text)
                # 移除末尾的斜杠
                directory_name = decoded.rstrip('/')
                directories.append(directory_name)
        
        return sorted(directories)
    
    except ET.ParseError as e:
        print(f"XML解析错误: {e}")
        return []
    except Exception as e:
        print(f"处理错误: {e}")
        return []

def main():
    # driver-operation-compliance-private XML内容
    xml_content = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?><ListBucketResult xmlns="http://obs.myhwclouds.com/doc/2015-06-30/"><Name>driver-operation-compliance-private</Name><Prefix></Prefix><Marker></Marker><MaxKeys>500</MaxKeys><Delimiter>%2F</Delimiter><IsTruncated>false</IsTruncated><EncodingType>url</EncodingType><Contents><Key>driver-resume-api00281d4bac58b6275759cc15eeb623ef</Key><LastModified>2024-06-13T03:27:24.679Z</LastModified><ETag>"6a99ed207694c27a9a01f2bc27fb7555"</ETag><Size>18715</Size><Owner><ID>0bed3a451880f3270f27c0148980a4c0</ID></Owner><StorageClass>STANDARD</StorageClass></Contents><CommonPrefixes><Prefix>asset-gateway%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>driver-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>driver-resume-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>mark-act%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>partner-workorder-web%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>recruit-rpa%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>resource-driver-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>sec-driving%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>taxi-admin-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>taxi-driver-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>taxi-league-web-api%2F</Prefix></CommonPrefixes></ListBucketResult>'''
    
    # 提取目录名
    directories = extract_directories_from_xml(xml_content)
    
    print("driver-operation-compliance-private 目录列表:")
    print("=" * 50)
    for i, directory in enumerate(directories, 1):
        print(f"{i:2d}. {directory}")
    
    print(f"\n总共找到 {len(directories)} 个目录")
    
    # 保存到文件
    with open('driver_directories.txt', 'w', encoding='utf-8') as f:
        for directory in directories:
            f.write(f"{directory}\n")
    
    print("\n目录名已保存到 driver_directories.txt 文件中")
    
    # 生成表格格式
    generate_table_formats(directories)

def generate_table_formats(directories):
    """生成不同格式的表格"""
    
    # Markdown表格格式
    print("\n" + "="*60)
    print("Markdown表格格式:")
    print("="*60)
    
    markdown_table = "| 序号 | 目录名 | 类型 |\n"
    markdown_table += "|------|--------|------|\n"
    
    for i, directory in enumerate(directories, 1):
        # 简单分类
        if 'api' in directory:
            category = 'API服务'
        elif 'web' in directory:
            category = 'Web应用'
        elif 'app' in directory:
            category = '移动应用'
        elif 'gateway' in directory:
            category = '网关服务'
        elif 'rpa' in directory:
            category = 'RPA服务'
        elif 'admin' in directory:
            category = '管理后台'
        else:
            category = '其他服务'
            
        markdown_table += f"| {i} | {directory} | {category} |\n"
    
    print(markdown_table)
    
    # 保存Markdown表格
    with open('driver_directories_table.md', 'w', encoding='utf-8') as f:
        f.write("# driver-operation-compliance-private 目录列表\n\n")
        f.write(markdown_table)
    
    print(f"\n文件已生成:")
    print("- driver_directories.txt (目录列表)")
    print("- driver_directories_table.md (Markdown表格)")

if __name__ == "__main__":
    main()
