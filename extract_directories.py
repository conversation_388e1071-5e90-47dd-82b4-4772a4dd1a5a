#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取XML中的目录名
"""

import xml.etree.ElementTree as ET
import urllib.parse

def extract_directories_from_xml(xml_content):
    """从XML内容中提取目录名"""
    try:
        # 解析XML
        root = ET.fromstring(xml_content)
        
        # 定义命名空间
        namespace = {'ns': 'http://obs.myhwclouds.com/doc/2015-06-30/'}
        
        # 查找所有CommonPrefixes元素
        common_prefixes = root.findall('.//ns:CommonPrefixes/ns:Prefix', namespace)
        
        directories = []
        for prefix in common_prefixes:
            if prefix.text:
                # URL解码
                decoded = urllib.parse.unquote(prefix.text)
                # 移除末尾的斜杠
                directory_name = decoded.rstrip('/')
                directories.append(directory_name)
        
        return sorted(directories)
    
    except ET.ParseError as e:
        print(f"XML解析错误: {e}")
        return []
    except Exception as e:
        print(f"处理错误: {e}")
        return []

def main():
    # XML内容
    xml_content = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?><ListBucketResult xmlns="http://obs.myhwclouds.com/doc/2015-06-30/"><Name>t3proprivate</Name><Prefix></Prefix><Marker></Marker><MaxKeys>500</MaxKeys><Delimiter>%2F</Delimiter><IsTruncated>false</IsTruncated><EncodingType>url</EncodingType><CommonPrefixes><Prefix>algo-grapics%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>asset-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>autodriver-connector-server%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>call-center%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>ccp-autodrive-backend-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>ccp-autodrive-safe-officer-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>chamtest%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>common-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>common-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>cua-user-api-c%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>driver-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>driver-caring%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>driver-resume-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>driver-voice%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>fault-handler%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>growth%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>img-processor%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>integrated-center%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>invoice-core-center%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>iov-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>iov%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>jpaas-system%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>maintain-data-config-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>mark-act%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>negative-manage-voice%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>negative-webapp-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>official-website-business-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>official-website-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>oms%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>open-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>operation-center%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>operation-message-proxy%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>org-manager-boss%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>orion-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>partner-support-fp%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>partner-workorder-web%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>passenger-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>resource-driver-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>resource-manager%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>risk-control-anti-multi-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>route-admin-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>route-pretrial%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>route%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>sec-driving%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>settlement-finance%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>smartva-operaserver%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>sp-sf-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-admin%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-bbs%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-cc-webapp-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-dc-dsp-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-it-open-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-mall-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-manager-car-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-manager-driver-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-manager-web%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3op-admin-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>taxi-admin-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>taxi-driver-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>taxi-h5-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>taxi-league-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>vehicle-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>vigen-web-api%2F</Prefix></CommonPrefixes></ListBucketResult>'''
    
    # 提取目录名
    directories = extract_directories_from_xml(xml_content)
    
    print("提取到的目录名:")
    print("=" * 50)
    for i, directory in enumerate(directories, 1):
        print(f"{i:2d}. {directory}")
    
    print(f"\n总共找到 {len(directories)} 个目录")
    
    # 保存到文件
    with open('directories.txt', 'w', encoding='utf-8') as f:
        for directory in directories:
            f.write(f"{directory}\n")
    
    print("\n目录名已保存到 directories.txt 文件中")

if __name__ == "__main__":
    main()
