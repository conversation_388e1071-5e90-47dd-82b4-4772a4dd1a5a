#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取XML中的目录名
"""

import xml.etree.ElementTree as ET
import urllib.parse

def extract_directories_from_xml(xml_content):
    """从XML内容中提取目录名"""
    try:
        # 解析XML
        root = ET.fromstring(xml_content)
        
        # 定义命名空间
        namespace = {'ns': 'http://obs.myhwclouds.com/doc/2015-06-30/'}
        
        # 查找所有CommonPrefixes元素
        common_prefixes = root.findall('.//ns:CommonPrefixes/ns:Prefix', namespace)
        
        directories = []
        for prefix in common_prefixes:
            if prefix.text:
                # URL解码
                decoded = urllib.parse.unquote(prefix.text)
                # 移除末尾的斜杠
                directory_name = decoded.rstrip('/')
                directories.append(directory_name)
        
        return sorted(directories)
    
    except ET.ParseError as e:
        print(f"XML解析错误: {e}")
        return []
    except Exception as e:
        print(f"处理错误: {e}")
        return []

def main():
    # XML内容
    xml_content = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?><ListBucketResult xmlns="http://obs.myhwclouds.com/doc/2015-06-30/"><Name>t3proprivate</Name><Prefix></Prefix><Marker></Marker><MaxKeys>500</MaxKeys><Delimiter>%2F</Delimiter><IsTruncated>false</IsTruncated><EncodingType>url</EncodingType><CommonPrefixes><Prefix>algo-grapics%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>asset-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>autodriver-connector-server%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>call-center%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>ccp-autodrive-backend-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>ccp-autodrive-safe-officer-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>chamtest%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>common-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>common-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>cua-user-api-c%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>driver-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>driver-caring%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>driver-resume-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>driver-voice%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>fault-handler%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>growth%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>img-processor%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>integrated-center%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>invoice-core-center%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>iov-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>iov%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>jpaas-system%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>maintain-data-config-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>mark-act%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>negative-manage-voice%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>negative-webapp-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>official-website-business-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>official-website-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>oms%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>open-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>operation-center%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>operation-message-proxy%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>org-manager-boss%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>orion-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>partner-support-fp%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>partner-workorder-web%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>passenger-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>resource-driver-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>resource-manager%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>risk-control-anti-multi-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>route-admin-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>route-pretrial%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>route%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>sec-driving%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>settlement-finance%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>smartva-operaserver%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>sp-sf-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-admin%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-bbs%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-cc-webapp-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-dc-dsp-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-it-open-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-mall-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-manager-car-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-manager-driver-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3-manager-web%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>t3op-admin-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>taxi-admin-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>taxi-driver-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>taxi-h5-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>taxi-league-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>vehicle-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>vigen-web-api%2F</Prefix></CommonPrefixes></ListBucketResult>'''
    
    # 提取目录名
    directories = extract_directories_from_xml(xml_content)
    
    print("提取到的目录名:")
    print("=" * 50)
    for i, directory in enumerate(directories, 1):
        print(f"{i:2d}. {directory}")
    
    print(f"\n总共找到 {len(directories)} 个目录")
    
    # 保存到文件
    with open('directories.txt', 'w', encoding='utf-8') as f:
        for directory in directories:
            f.write(f"{directory}\n")
    
    print("\n目录名已保存到 directories.txt 文件中")

    # 生成表格格式
    generate_table_formats(directories)

def generate_table_formats(directories):
    """生成不同格式的表格"""

    # 1. Markdown表格格式
    print("\n" + "="*60)
    print("Markdown表格格式:")
    print("="*60)

    markdown_table = "| 序号 | 目录名 | 类型 |\n"
    markdown_table += "|------|--------|------|\n"

    for i, directory in enumerate(directories, 1):
        # 简单分类
        if 'api' in directory:
            category = 'API服务'
        elif 'web' in directory:
            category = 'Web应用'
        elif 'app' in directory:
            category = '移动应用'
        elif 'admin' in directory:
            category = '管理后台'
        elif 'center' in directory:
            category = '中心服务'
        else:
            category = '其他服务'

        markdown_table += f"| {i} | {directory} | {category} |\n"

    print(markdown_table)

    # 保存Markdown表格
    with open('directories_table.md', 'w', encoding='utf-8') as f:
        f.write("# 目录列表\n\n")
        f.write(markdown_table)

    # 2. CSV格式
    print("\n" + "="*60)
    print("CSV表格格式:")
    print("="*60)

    csv_content = "序号,目录名,类型\n"
    for i, directory in enumerate(directories, 1):
        if 'api' in directory:
            category = 'API服务'
        elif 'web' in directory:
            category = 'Web应用'
        elif 'app' in directory:
            category = '移动应用'
        elif 'admin' in directory:
            category = '管理后台'
        elif 'center' in directory:
            category = '中心服务'
        else:
            category = '其他服务'
        csv_content += f"{i},{directory},{category}\n"

    print(csv_content[:500] + "..." if len(csv_content) > 500 else csv_content)

    # 保存CSV文件
    with open('directories_table.csv', 'w', encoding='utf-8') as f:
        f.write(csv_content)

    # 3. HTML表格格式
    html_table = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>目录列表</title>
    <style>
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
    </style>
</head>
<body>
    <h1>目录列表</h1>
    <table>
        <tr>
            <th>序号</th>
            <th>目录名</th>
            <th>类型</th>
        </tr>
"""

    for i, directory in enumerate(directories, 1):
        if 'api' in directory:
            category = 'API服务'
        elif 'web' in directory:
            category = 'Web应用'
        elif 'app' in directory:
            category = '移动应用'
        elif 'admin' in directory:
            category = '管理后台'
        elif 'center' in directory:
            category = '中心服务'
        else:
            category = '其他服务'
        html_table += f"        <tr><td>{i}</td><td>{directory}</td><td>{category}</td></tr>\n"

    html_table += """    </table>
</body>
</html>"""

    # 保存HTML文件
    with open('directories_table.html', 'w', encoding='utf-8') as f:
        f.write(html_table)

    print(f"\n文件已生成:")
    print("- directories_table.md (Markdown格式)")
    print("- directories_table.csv (CSV格式)")
    print("- directories_table.html (HTML格式)")

if __name__ == "__main__":
    main()
