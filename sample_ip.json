{"code": 200, "data": [{"servicename": "iov-web-api", "namespace": "application.yml", "line": "行号:17        host: 127.0.0.1"}, {"servicename": "fault-handler", "namespace": "application.yml", "line": "行号:24        host: 127.0.0.1"}, {"servicename": "fault-handler", "namespace": "application.yml", "line": "行号:170    route: http://**********:8085/needcheck"}, {"servicename": "autodriver-connector-server", "namespace": "application.yml", "line": "行号:38        bootstrap-servers: **************:9092,**************:9092,**************:9092"}, {"servicename": "t3op-admin-api", "namespace": "application.yml", "line": "行号:15        host: 127.0.0.1"}, {"servicename": "integrated-center", "namespace": "application.yml", "line": "行号:238      host: ************:2443"}, {"servicename": "integrated-center", "namespace": "application.yml", "line": "行号:256      url: http://*************:5050"}, {"servicename": "common-app-api", "namespace": "application.yml", "line": "行号:31        host: 127.0.0.1"}, {"servicename": "route-admin-api", "namespace": "application.yml", "line": "行号:27        host: 127.0.0.1"}, {"servicename": "passenger-app-api", "namespace": "application.yml", "line": "行号:34        host: 127.0.0.1"}, {"servicename": "resource-manager", "namespace": "application.yml", "line": "行号:112      url: http://*************:9080/snap/omaui/custom/faw/vdb/tsp_in"}, {"servicename": "t3-it-open-api", "namespace": "application.yml", "line": "行号:74        host: 127.0.0.1"}, {"servicename": "t3-it-open-api", "namespace": "application.yml", "line": "行号:103      urls: ldaps://*********"}, {"servicename": "asset-gateway", "namespace": "application.yml", "line": "行号:8      # Redis数据库索引（默认为0  ************"}, {"servicename": "driver-app-api", "namespace": "application.yml", "line": "行号:31        host: 127.0.0.1"}, {"servicename": "taxi-admin-web-api", "namespace": "application.yml", "line": "行号:15        host: 127.0.0.1"}, {"servicename": "taxi-driver-app-api", "namespace": "application.yml", "line": "行号:34        host: 127.0.0.1"}, {"servicename": "negative-webapp-api", "namespace": "application.yml", "line": "行号:24        host: 127.0.0.1"}, {"servicename": "partner-workorder-web", "namespace": "application.yml", "line": "行号:16        host: 127.0.0.1"}, {"servicename": "sec-driving", "namespace": "application.yml", "line": "行号:12        host: 127.0.0.1"}]}