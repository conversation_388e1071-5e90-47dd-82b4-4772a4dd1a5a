#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取iov-media-private XML中的目录名
"""

import xml.etree.ElementTree as ET
import urllib.parse

def extract_directories_from_xml(xml_content):
    """从XML内容中提取目录名"""
    try:
        # 解析XML
        root = ET.fromstring(xml_content)
        
        # 定义命名空间
        namespace = {'ns': 'http://obs.myhwclouds.com/doc/2015-06-30/'}
        
        # 查找所有CommonPrefixes元素
        common_prefixes = root.findall('.//ns:CommonPrefixes/ns:Prefix', namespace)
        
        directories = []
        for prefix in common_prefixes:
            if prefix.text:
                # URL解码
                decoded = urllib.parse.unquote(prefix.text)
                # 移除末尾的斜杠
                directory_name = decoded.rstrip('/')
                directories.append(directory_name)
        
        return sorted(directories)
    
    except ET.ParseError as e:
        print(f"XML解析错误: {e}")
        return []
    except Exception as e:
        print(f"处理错误: {e}")
        return []

def main():
    # iov-media-private XML内容
    xml_content = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?><ListBucketResult xmlns="http://obs.myhwclouds.com/doc/2015-06-30/"><Name>iov-media-private</Name><Prefix></Prefix><Marker></Marker><MaxKeys>500</MaxKeys><Delimiter>%2F</Delimiter><IsTruncated>false</IsTruncated><EncodingType>url</EncodingType><CommonPrefixes><Prefix>ai-event%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>autodriver-connector-server%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>call-center%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>cc-core%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>cc-knowledge%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>ccp-autodrive-backend-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>ccp-autodrive-operation-manage%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>common-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>driver-voice%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>fault-handler%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>iov-web-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>iov%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>lbs-fence%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>maintain-data-config-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>mark-act%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>negative-webapp-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>partner-workorder-web%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>resource-driver-app-api%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>route-event%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>sec-driving%2F</Prefix></CommonPrefixes><CommonPrefixes><Prefix>smartva-operaserver%2F</Prefix></CommonPrefixes></ListBucketResult>'''
    
    # 提取目录名
    directories = extract_directories_from_xml(xml_content)
    
    print("iov-media-private 目录列表:")
    print("=" * 50)
    for i, directory in enumerate(directories, 1):
        print(f"{i:2d}. {directory}")
    
    print(f"\n总共找到 {len(directories)} 个目录")
    
    # 保存到文件
    with open('iov_directories.txt', 'w', encoding='utf-8') as f:
        for directory in directories:
            f.write(f"{directory}\n")
    
    print("\n目录名已保存到 iov_directories.txt 文件中")
    
    # 生成表格格式
    generate_table_formats(directories)

def generate_table_formats(directories):
    """生成不同格式的表格"""
    
    # Markdown表格格式
    print("\n" + "="*60)
    print("Markdown表格格式:")
    print("="*60)
    
    markdown_table = "| 序号 | 目录名 | 类型 |\n"
    markdown_table += "|------|--------|------|\n"
    
    for i, directory in enumerate(directories, 1):
        # 简单分类
        if 'api' in directory:
            category = 'API服务'
        elif 'web' in directory:
            category = 'Web应用'
        elif 'app' in directory:
            category = '移动应用'
        elif 'center' in directory:
            category = '中心服务'
        elif 'core' in directory:
            category = '核心服务'
        elif 'event' in directory:
            category = '事件服务'
        elif 'manage' in directory:
            category = '管理服务'
        else:
            category = '其他服务'
            
        markdown_table += f"| {i} | {directory} | {category} |\n"
    
    print(markdown_table)
    
    # 保存Markdown表格
    with open('iov_directories_table.md', 'w', encoding='utf-8') as f:
        f.write("# iov-media-private 目录列表\n\n")
        f.write(markdown_table)
    
    print(f"\n文件已生成:")
    print("- iov_directories.txt (目录列表)")
    print("- iov_directories_table.md (Markdown表格)")

if __name__ == "__main__":
    main()
