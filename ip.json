{"code": 200, "data": [{"servicename": "iov-resource-simcard-qianxun", "namespace": "application", "line": "行号:3  hbase.host: **********:2181,********:2181,*********:2181"}, {"servicename": "iov-resource-simcard-qianxun", "namespace": "application", "line": "行号:4  kafka.hosts: *********:9092,*********:9092,*********:9092"}, {"servicename": "iov-resource-simcard-qianxun", "namespace": "application", "line": "行号:8  mongodb.ip: *********"}, {"servicename": "big-screen-monitor", "namespace": "application.yml", "line": "行号:9      url: jdbc:mysql://*********:3336/t3_big_screen?useUnicode=true&characterEncoding=utf8&nullNamePatternMatchesAll=true&useSSL=false&serverTimezone=GMT%2B8"}, {"servicename": "iov-integrated-party", "namespace": "application.yml", "line": "行号:60      serverIp: ***********"}, {"servicename": "iov-web-api", "namespace": "application.yml", "line": "行号:17        host: 127.0.0.1"}, {"servicename": "iov-api-gateway", "namespace": "application.yml", "line": "行号:33        host: 127.0.0.1"}, {"servicename": "signalServer", "namespace": "application.yml", "line": "行号:7        host: 127.0.0.1"}, {"servicename": "signalServer", "namespace": "application.yml", "line": "行号:62    streamDistributeUrl: **********"}, {"servicename": "signalServer", "namespace": "application.yml", "line": "行号:71    mediaServerUrl: **********"}, {"servicename": "biz-iov-alarm-state-calc", "namespace": "application", "line": "行号:7  hbase.host: **********:2181,********:2181,*********:2181"}, {"servicename": "biz-iov-alarm-state-calc", "namespace": "application", "line": "行号:8  kafka.hosts: *********:9092,*********:9092,*********:9092"}, {"servicename": "fault-handler", "namespace": "application.yml", "line": "行号:24        host: 127.0.0.1"}, {"servicename": "fault-handler", "namespace": "application.yml", "line": "行号:170    route: http://**********:8085/needcheck"}, {"servicename": "biz-iov-release-alarm-record-etl", "namespace": "application", "line": "行号:7  hbase.host: **********:2181,********:2181,*********:2181"}, {"servicename": "biz-iov-release-alarm-record-etl", "namespace": "application", "line": "行号:8  kafka.hosts: *********:9092,*********:9092,*********:9092"}, {"servicename": "iov-console", "namespace": "application.yml", "line": "行号:25        host: 127.0.0.1"}, {"servicename": "vehicle-device-api", "namespace": "application.yml", "line": "行号:12        host: 127.0.0.1"}, {"servicename": "iov-location-stay-cal", "namespace": "application", "line": "行号:4  kafka.alarm.hosts: *********:9092,*********:9092,*********:9092"}, {"servicename": "iov-location-stay-cal", "namespace": "application", "line": "行号:5  kafka.gis.hosts: *********:9092,*********:9092,*********:9092"}, {"servicename": "iov-operation-to-es-hbase", "namespace": "application", "line": "行号:2  es.host: *********"}, {"servicename": "iov-resource-to-es-hbase", "namespace": "application", "line": "行号:7  hbase.host: **********:2181,********:2181,*********:2181"}, {"servicename": "iov-resource-to-es-hbase", "namespace": "application", "line": "行号:8  kafka.hosts: *********:9092,*********:9092,*********:9092"}, {"servicename": "iov-resource-to-t3-iov", "namespace": "application", "line": "行号:2  kafka.hosts: *********:9092,*********:9092,*********:9092"}, {"servicename": "biz-vehicle-moved-mile", "namespace": "application", "line": "行号:2  kafka.iov.hosts: **********:9092,**********:9092,********:9092"}, {"servicename": "biz-vehicle-moved-mile", "namespace": "application", "line": "行号:3  phoenix.host: **********:2181,********:2181,*********:2181"}, {"servicename": "smartva-openplatform", "namespace": "application.yml", "line": "行号:23        host: 127.0.0.1"}, {"servicename": "smartva-openplatform", "namespace": "application.yml", "line": "行号:280  POLICE_AI_ALARM_URL: https://**********:8085/needcheck/v1/infers/209936e5-4968-48c1-98cb-87d9b6a75343/api/v1/stream/ai/monitor"}, {"servicename": "iov-t3-iov-to-es-hbase", "namespace": "application", "line": "行号:7  hbase.host: **********:2181,********:2181,*********:2181"}, {"servicename": "iov-t3-iov-to-es-hbase", "namespace": "application", "line": "行号:8  kafka.biz.hosts: *********:9092,*********:9092,*********:9092"}, {"servicename": "iov-t3-iov-to-es-hbase", "namespace": "application", "line": "行号:9  kafka.hosts: *********:9092,*********:9092,*********:9092"}, {"servicename": "iov-business", "namespace": "application.yml", "line": "行号:25        host: 127.0.0.1"}, {"servicename": "iov-driving-behavior-alarm", "namespace": "application", "line": "行号:2  kafka.consumer.hosts: **********:9092,**********:9092,********:9092"}, {"servicename": "iov-driving-behavior-alarm", "namespace": "application", "line": "行号:3  kafka.producer.hosts: *********:9092,*********:9092,*********:9092"}, {"servicename": "autodriver-data-server", "namespace": "application.yml", "line": "行号:59        bootstrap-servers: ************:9092"}, {"servicename": "autodriver-data-server", "namespace": "application.yml", "line": "行号:111    url: ******************************************"}, {"servicename": "iov-rapid-acc-calc", "namespace": "application.yml", "line": "行号:8      address: zookeeper://************:2181?backup=************:2181,************:2181"}, {"servicename": "iov-rapid-acc-calc", "namespace": "application.yml", "line": "行号:19    server-addresses: http://************:8080/t3-job-admin"}, {"servicename": "iov-rapid-acc-calc", "namespace": "application.yml", "line": "行号:32    serverlists: ************:2181,************:2181,************:2181  #对应环境zk"}, {"servicename": "iov-rapid-acc-calc", "namespace": "application.yml", "line": "行号:51  serialNumStatisticUrl: http://***********:80/open-api/audit/v1/msg/serialNumStatistic"}, {"servicename": "autodriver-gateway-api", "namespace": "application.yml", "line": "行号:19        host: 127.0.0.1"}, {"servicename": "autodriver-config-server", "namespace": "application.yml", "line": "行号:75        bootstrap-servers: **************:9092,**************:9092,**************:9092"}, {"servicename": "iov-flink-clickhouse", "namespace": "application", "line": "行号:6  dataSource.clickhouse.hosts: **********:8123;**********:8123;**********:8123"}, {"servicename": "iov-manage", "namespace": "application.yml", "line": "行号:128    ip: '{T320100:''*************'',T320500:''************'',T440400:''***************'',T220100:''*************'',T620100:''lzcz.hstgps.com'',T420100:''***************''}'"}, {"servicename": "iov-rule-engine", "namespace": "application.yml", "line": "行号:12        host: 127.0.0.1"}, {"servicename": "lampstate-api", "namespace": "application.yml", "line": "行号:10        host: 127.0.0.1"}, {"servicename": "iov-app-api", "namespace": "application.yml", "line": "行号:27        host: 127.0.0.1"}, {"servicename": "autodriver-connector-server", "namespace": "application.yml", "line": "行号:38        bootstrap-servers: **************:9092,**************:9092,**************:9092"}, {"servicename": "iov-devices-gateway", "namespace": "application.yml", "line": "行号:59    downLinkIp: ************"}, {"servicename": "autodriver-web-api", "namespace": "application.yml", "line": "行号:13        host: 127.0.0.1"}, {"servicename": "autodriver-web-api", "namespace": "application.yml", "line": "行号:23      # Redis数据库索引（默认为0  ************"}, {"servicename": "iov-data-gateway", "namespace": "application.yml", "line": "行号:12        host: 127.0.0.1"}, {"servicename": "t3op-admin-api", "namespace": "application.yml", "line": "行号:15        host: 127.0.0.1"}, {"servicename": "integrated-center", "namespace": "application.yml", "line": "行号:238      host: 218.95.89.32:2443"}, {"servicename": "integrated-center", "namespace": "application.yml", "line": "行号:256      url: http://183.246.59.71:5050"}, {"servicename": "integrated-center", "namespace": "alipay.yml", "line": "行号:31        url: http://***********:6002/api/tel"}, {"servicename": "integrated-center", "namespace": "alipay.yml", "line": "行号:37        url: http://39.105.45.123:8081/api/tel"}, {"servicename": "integrated-center", "namespace": "alipay.yml", "line": "行号:39        url: http://61.147.15.34:18602/callout/voiceNotification/notifyMsgSubmit"}, {"servicename": "integrated-center", "namespace": "alipay.yml", "line": "行号:203        url: http://117.160.222.29:9890/zztaxi/out/batchQueryZxkh.do"}, {"servicename": "agg-core-service", "namespace": "application.yml", "line": "行号:100      ignoreExceptionConfigMap: '{\"http://**********:8085/needcheck/api/t3/order/estimate\":5,\"http://car-travel.17usoft.net/platform/t3/v2/order/estimate\":10,\"http://123.60.187.250:8888/magic-box/t3/v2/order/estimate\":10}'"}, {"servicename": "agg-core-service", "namespace": "application.yml", "line": "行号:230      # uri: https://**********:8085/needcheck/merchant/v1"}, {"servicename": "operation-flink-realtime", "namespace": "application", "line": "行号:55  mysql.url: jdbc:mysql://*********:3336/t3go_datalake?useUnicode=true&characterEncoding=utf8&nullNamePatternMatchesAll=true&useSSL=false&serverTimezone=GMT%2B8"}, {"servicename": "operation-flink-realtime", "namespace": "application", "line": "行号:56  mysql.url.ai: jdbc:mysql://*********:3336/t3_ai?characterEncoding=utf8&useSSL=true"}, {"servicename": "operation-flink-realtime", "namespace": "application", "line": "行号:57  mysql.url.bs: jdbc:mysql://*********:3336/t3_big_screen?useUnicode=true&characterEncoding=utf8&nullNamePatternMatchesAll=true&useSSL=false&serverTimezone=GMT%2B8"}, {"servicename": "route-config", "namespace": "application.yml", "line": "行号:78          tags: version=**********:8085/needcheck,author=z<PERSON><PERSON><PERSON>"}, {"servicename": "operation-monitor", "namespace": "application.yml", "line": "行号:9      url: jdbc:mysql://*********:3336/t3go_datalake?useUnicode=true&characterEncoding=utf8&nullNamePatternMatchesAll=true&useSSL=false&serverTimezone=GMT%2B8"}, {"servicename": "operation-monitor", "namespace": "application.yml", "line": "行号:80      url: http://************:8080/requestCarStatus"}, {"servicename": "integrated-message", "namespace": "application-1.yml", "line": "行号:32        urlSingle: http://*************:8803/sms/v2/std/send_single"}, {"servicename": "integrated-message", "namespace": "application-1.yml", "line": "行号:33        urlBatch: http://*************:8803/sms/v2/std/send_mult"}, {"servicename": "t3op-internal-api", "namespace": "application.yml", "line": "行号:5        host: 127.0.0.1"}, {"servicename": "common-app-api", "namespace": "application.yml", "line": "行号:31        host: 127.0.0.1"}, {"servicename": "common-app-api", "namespace": "application.yml", "line": "行号:161        androidVersion: P_a_1.0.0,P_a_1.0.0.0,P_a_1.0.1,P_a_1.0.2,P_a_1.0.3,P_a_1.0.4,P_a_1.0.5,P_a_1.0.6,P_a_1.0.7,P_a_1.0.8,P_a_1.0.9,P_a_1.0.9.1,P_a_1.0.9.2,P_a_1.0.9.3,P_a_1.0.10,P_a_1.0.10.1,P_a_1.0.10.2,P_a_1.0.10.3,P_a_1.0.10.4,P_a_1.0.11,P_a_1.0.11.4,P_a_1.0.12,P_a_1.0.13,P_a_1.0.14,P_a_1.0.14.5,P_a_1.0.14.6,P_a_1.0.14.7,P_a_1.0.14.8,P_a_1.0.14.9,P_a_1.0.15"}, {"servicename": "common-app-api", "namespace": "application.yml", "line": "行号:315  face.thirdUrl: https://**********:8085/needcheck"}, {"servicename": "common-app-api", "namespace": "application-extend.yml", "line": "行号:93      image2x: https://**********:8085/needcheck/fastcar_hx_2x_7753e40d-e091-4056-b529-69fe8c39f5d0.png"}, {"servicename": "common-app-api", "namespace": "application-extend.yml", "line": "行号:94      image3x: https://**********:8085/needcheck/fastcar_hx_3x_2c984c71-f2e8-40d7-997e-024f91e957de.png"}, {"servicename": "aggflow-core-service", "namespace": "open.aggflow-biz-public.yml", "line": "行号:97    notifyUrl: http://**********:8085/needcheck/router/rest"}, {"servicename": "aggflow-core-service", "namespace": "open.aggflow-biz-public.yml", "line": "行号:120    baseUrl: https://**********:8085/needcheck"}, {"servicename": "aggflow-core-service", "namespace": "open.aggflow-biz-public.yml", "line": "行号:130      url: https://**********:8085/needcheck"}, {"servicename": "aggflow-core-service", "namespace": "open.aggflow-biz-public.yml", "line": "行号:134    host: https://**********:8085/needcheck"}, {"servicename": "solution-carriage", "namespace": "application.yml", "line": "行号:42        host: 127.0.0.1"}, {"servicename": "route-admin-api", "namespace": "application.yml", "line": "行号:8      # Redis数据库索引（默认为0  ************"}, {"servicename": "route-admin-api", "namespace": "application.yml", "line": "行号:27        host: 127.0.0.1"}, {"servicename": "passenger-web-api", "namespace": "application.yml", "line": "行号:16        host: 127.0.0.1"}, {"servicename": "solution-trip-general", "namespace": "application.yml", "line": "行号:29        host: 127.0.0.1"}, {"servicename": "solution-trip-general", "namespace": "application.yml", "line": "行号:109        androidVersion: P_a_1.0.0,P_a_1.0.0.0,P_a_1.0.1,P_a_1.0.2,P_a_1.0.3,P_a_1.0.4,P_a_1.0.5,P_a_1.0.6,P_a_1.0.7,P_a_1.0.8,P_a_1.0.9,P_a_1.0.9.1,P_a_1.0.9.2,P_a_1.0.9.3,P_a_1.0.10,P_a_1.0.10.1,P_a_1.0.10.2,P_a_1.0.10.3,P_a_1.0.10.4,P_a_1.0.11,P_a_1.0.11.4,P_a_1.0.12,P_a_1.0.13,P_a_1.0.14,P_a_1.0.14.5,P_a_1.0.14.6,P_a_1.0.14.7,P_a_1.0.14.8,P_a_1.0.14.9,P_a_1.0.15"}, {"servicename": "solution-trip-general", "namespace": "application.yml", "line": "行号:118      host: '{420100: ''https://auth-wuhan.i-xiaoma.com.cn:1443'',120100: ''http://**************:18000/healthcode/Health/code/query''}'"}, {"servicename": "passenger-app-api", "namespace": "application.yml", "line": "行号:34        host: 127.0.0.1"}, {"servicename": "passenger-app-api", "namespace": "application.yml", "line": "行号:100        androidVersion: P_a_1.0.0,P_a_1.0.0.0,P_a_1.0.1,P_a_1.0.2,P_a_1.0.3,P_a_1.0.4,P_a_1.0.5,P_a_1.0.6,P_a_1.0.7,P_a_1.0.8,P_a_1.0.9,P_a_1.0.9.1,P_a_1.0.9.2,P_a_1.0.9.3,P_a_1.0.10,P_a_1.0.10.1,P_a_1.0.10.2,P_a_1.0.10.3,P_a_1.0.10.4,P_a_1.0.11,P_a_1.0.11.4,P_a_1.0.12,P_a_1.0.13,P_a_1.0.14,P_a_1.0.14.5,P_a_1.0.14.6,P_a_1.0.14.7,P_a_1.0.14.8,P_a_1.0.14.9,P_a_1.0.15"}, {"servicename": "passenger-app-api", "namespace": "application.yml", "line": "行号:107      host: '{420100: ''https://auth-wuhan.i-xiaoma.com.cn:1443'',120100: ''http://**************:18000/healthcode/Health/code/query''}'"}, {"servicename": "route-sink-hbase", "namespace": "application", "line": "行号:7  hbase.zk.hosts.passenger: **********:2181,********:2181,*********:2181"}, {"servicename": "route-sink-hbase", "namespace": "application", "line": "行号:8  hbase.zk.hosts.route: **********:2181,********:2181,*********:2181"}, {"servicename": "route-sink-hbase", "namespace": "application", "line": "行号:9  kafka.zk.hosts.route: **********:9095"}, {"servicename": "route-sink-hbase", "namespace": "application", "line": "行号:19  mysql.url: *****************************************************************************"}, {"servicename": "business-config", "namespace": "application.yml", "line": "行号:40        host: 127.0.0.1"}, {"servicename": "push-server", "namespace": "application.yml", "line": "行号:24      # Redis数据库索引（默认为0  ************"}, {"servicename": "t3-sentinel", "namespace": "middleware.application", "line": "行号:3  middleware.cmd.server: ************:20948"}, {"servicename": "iov-data-manage", "namespace": "application.yml", "line": "行号:23        host: 127.0.0.1"}, {"servicename": "iov-data-manage", "namespace": "application.yml", "line": "行号:54      # Redis数据库索引（默认为0  ************"}, {"servicename": "search-admin", "namespace": "application.yml", "line": "行号:118      serverMonitorUrl: https://dolphin-kibana-test.t3go.net/app/kibana#/dashboard/d7edb790-c289-11ea-924a-5bcd2daf1709?embed=true&_g=(refreshInterval:('$$hashKey':'object:2003',display:'10+seconds',pause:!f,section:1,value:5000),time:(from:now-15m,mode:quick,to:now))&_a=(description:'',filters:!(),fullScreenMode:!f,options:(darkTheme:!f,hidePanelTitles:!f,useMargins:!t),panels:!((embeddableConfig:(vis:(colors:('***********':%23806EB7,'***********':%23052B51),legendOpen:!f)),gridData:(h:16,i:'1',w:16,x:0,y:13),id:'4f4407f0-bf32-11ea-924a-5bcd2daf1709',panelIndex:'1',type:visualization,version:'6.3.2'),(embeddableConfig:(vis:(legendOpen:!f)),gridData:(h:16,i:'2',w:16,x:32,y:13),id:acb42aa0-bf32-11ea-924a-5bcd2daf1709,panelIndex:'2',type:visualization,version:'6.3.2'),(embeddableConfig:(vis:(colors:('***********:+%E5%8F%AF%E7%94%A8%E5%86%85%E5%AD%98':%2370DBED),legendOpen:!f)),gridData:(h:16,i:'3',w:16,x:16,y:13),id:'029d6c10-bf33-11ea-924a-5bcd2daf1709',panelIndex:'3',type:visualization,version:'6.3.2'),(embeddableConfig:(vis:(defaultColors:('0+-+50':'rgb(0,104,55)','50+-+75':'rgb(255,255,190)','75+-+100':'rgb(165,0,38)'),legendOpen:!f)),gridData:(h:13,i:'5',w:10,x:18,y:0),id:'9d215dc0-c260-11ea-924a-5bcd2daf1709',panelIndex:'5',type:visualization,version:'6.3.2'),(embeddableConfig:(vis:(defaultColors:('0+-+50':'rgb(0,104,55)','50+-+75':'rgb(255,255,190)','75+-+100':'rgb(165,0,38)'),legendOpen:!f)),gridData:(h:13,i:'6',w:10,x:38,y:0),id:d5cd4530-c260-11ea-924a-5bcd2daf1709,panelIndex:'6',type:visualization,version:'6.3.2'),(embeddableConfig:(vis:(defaultColors:('0+-+50':'rgb(0,104,55)','50+-+75':'rgb(255,255,190)','75+-+100':'rgb(165,0,38)'),legendOpen:!f)),gridData:(h:13,i:'7',w:10,x:28,y:0),id:fad678b0-c260-11ea-924a-5bcd2daf1709,panelIndex:'7',type:visualization,version:'6.3.2'),(embeddableConfig:(vis:(params:(sort:(columnIndex:1,direction:desc)))),gridData:(h:13,i:'8',w:18,x:0,y:0),id:ba1aa2b0-c288-11ea-924a-5bcd2daf1709,panelIndex:'8',type:visualization,version:'6.3.2')),query:(language:lucene,query:'appId:dolphin-collect'),timeRestore:!f,title:%E9%87%87%E9%9B%86%E6%9C%8D%E5%8A%A1%E7%9B%91%E6%8E%A7,viewMode:view)"}, {"servicename": "search-admin", "namespace": "application.yml", "line": "行号:120      serverMonitorUrl: https://dolphin-kibana-test.t3go.net/app/kibana#/dashboard/0671b470-c287-11ea-924a-5bcd2daf1709?embed=true&_g=(refreshInterval:('$$hashKey':'object:2003',display:'10+seconds',pause:!f,section:1,value:5000),time:(from:now-15m,mode:quick,to:now))&_a=(description:'',filters:!(),fullScreenMode:!f,options:(darkTheme:!f,hidePanelTitles:!f,useMargins:!t),panels:!((embeddableConfig:(vis:(colors:('***********':%23806EB7,'***********':%23052B51),legendOpen:!f)),gridData:(h:16,i:'1',w:16,x:0,y:13),id:'4f4407f0-bf32-11ea-924a-5bcd2daf1709',panelIndex:'1',type:visualization,version:'6.3.2'),(embeddableConfig:(vis:(legendOpen:!f)),gridData:(h:16,i:'2',w:16,x:32,y:13),id:acb42aa0-bf32-11ea-924a-5bcd2daf1709,panelIndex:'2',type:visualization,version:'6.3.2'),(embeddableConfig:(vis:(colors:('***********:+%E5%8F%AF%E7%94%A8%E5%86%85%E5%AD%98':%2370DBED),legendOpen:!f)),gridData:(h:16,i:'3',w:16,x:16,y:13),id:'029d6c10-bf33-11ea-924a-5bcd2daf1709',panelIndex:'3',type:visualization,version:'6.3.2'),(embeddableConfig:(vis:(defaultColors:('0+-+50':'rgb(0,104,55)','50+-+75':'rgb(255,255,190)','75+-+100':'rgb(165,0,38)'),legendOpen:!f)),gridData:(h:13,i:'5',w:10,x:18,y:0),id:'9d215dc0-c260-11ea-924a-5bcd2daf1709',panelIndex:'5',type:visualization,version:'6.3.2'),(embeddableConfig:(vis:(defaultColors:('0+-+50':'rgb(0,104,55)','50+-+75':'rgb(255,255,190)','75+-+100':'rgb(165,0,38)'),legendOpen:!f)),gridData:(h:13,i:'6',w:10,x:38,y:0),id:d5cd4530-c260-11ea-924a-5bcd2daf1709,panelIndex:'6',type:visualization,version:'6.3.2'),(embeddableConfig:(vis:(defaultColors:('0+-+50':'rgb(0,104,55)','50+-+75':'rgb(255,255,190)','75+-+100':'rgb(165,0,38)'),legendOpen:!f)),gridData:(h:13,i:'7',w:10,x:28,y:0),id:fad678b0-c260-11ea-924a-5bcd2daf1709,panelIndex:'7',type:visualization,version:'6.3.2'),(embeddableConfig:(vis:(params:(sort:(columnIndex:1,direction:desc)))),gridData:(h:13,i:'8',w:18,x:0,y:0),id:ba1aa2b0-c288-11ea-924a-5bcd2daf1709,panelIndex:'8',type:visualization,version:'6.3.2')),query:(language:lucene,query:'appId:dolphin-proxy'),timeRestore:!f,title:%E4%BB%A3%E7%90%86%E6%9C%8D%E5%8A%A1%E7%9B%91%E6%8E%A7,viewMode:view)"}, {"servicename": "push-business", "namespace": "application.yml", "line": "行号:24      # Redis数据库索引（默认为0  ************"}, {"servicename": "t3-mq-proxy", "namespace": "application.yml", "line": "行号:31    center: ************"}, {"servicename": "t3-mq-proxy", "namespace": "application.yml", "line": "行号:46      url: http://************:8081/t3-mq-proxy/api/metric/accept-metric"}, {"servicename": "t3-mq-proxy", "namespace": "application.yml", "line": "行号:77        address: ************"}, {"servicename": "t3-mq-chronos", "namespace": "application.yml", "line": "行号:95    cproxyAddrs: ************:8081 #分号分割"}, {"servicename": "t3-mq-chronos", "namespace": "application.yml", "line": "行号:104    pproxyAddrs: ************:8081"}, {"servicename": "t3-mq-chronos-master", "namespace": "application.yml", "line": "行号:95    cproxyAddrs: ************:8081 #分号分割"}, {"servicename": "t3-mq-chronos-master", "namespace": "application.yml", "line": "行号:104    pproxyAddrs: ************:8081"}, {"servicename": "t3-mq-chronos-slave", "namespace": "application.yml", "line": "行号:95    cproxyAddrs: ************:8081 #分号分割"}, {"servicename": "t3-mq-chronos-slave", "namespace": "application.yml", "line": "行号:104    pproxyAddrs: ************:8081"}, {"servicename": "solution-passenger-api", "namespace": "application.yml", "line": "行号:280    callPageBottomImage: https://**********:8085/needcheck/3b41f6fc-3eaf-4e08-816d-64e79b6a070a.png"}, {"servicename": "component-test", "namespace": "application.yml", "line": "行号:12        host: 127.0.0.1"}, {"servicename": "component-test", "namespace": "application.yml", "line": "行号:66      bootstrap-servers: **********:9092,**********:9092,**********:9092"}, {"servicename": "component-test", "namespace": "application.yml", "line": "行号:126        hosts: *************"}, {"servicename": "component-test", "namespace": "application.yml", "line": "行号:190          bootstrap-servers: **********:9092,**********:9092,**********:9092"}, {"servicename": "component-remote", "namespace": "application.yml", "line": "行号:72      bootstrap-servers: **********:9092,**********:9092,**********:9092"}, {"servicename": "mock-server", "namespace": "application.yml", "line": "行号:41      bootstrap-servers: **********:9092,**********:9092,**********:9092"}, {"servicename": "poseidon-alarm-server", "namespace": "application", "line": "行号:5  invalidInstanceList: hw-prd-dtp-mrs-10-4-47-218,hw-prd-dtp-mrs-10-4-46-155,hw-prd-dtp-mrs-10-4-45-150,hw-prd-dtp-mrs-10-4-40-76,hw-prd-dtp-mrs-10-4-43-40,hw-prd-dtp-bi-tools-10-4-47-28,hw-prd-dtp-mrs-10-4-47-117,hw-prd-dtp-mrs-10-4-41-38,hw-prd-dtp-bi-tools-10-4-41-165,hw-prd-dtp-mrs-10-4-44-56,hw-prd-dtp-mrs-10-4-47-51,rm-6el9m6qrb61c7d44n.mysql.rds.ops.t3go.com,hw-prd-drv-dri-churn-10-4-1-79,qianxin-proxy,hw-prd-dtp-mrs-10-4-43-139,af7e33b2-836d-495d-b406-ea09d416891d_node_ana_coreaqedZ-0002,af7e33b2-836d-495d-b406-ea09d416891d_node_ana_coreaqedZ-0001,hw-prd-dtp-mrs-10-4-44-23,hw-prd-bdplat-mrs-10-4-42-186,hw-prd-dtp-mrs-10-4-40-236,hw-prd-dtp-mrs-10-4-43-100,hw-prd-dtp-mrs-10-4-43-148,hw-prd-dtp-mrs-10-4-43-45,hw-prd-bdplat-mrs-10-4-44-203,hw-prd-bdplat-mrs-10-4-44-215,hw-prd-dtp-mrs-10-4-44-54,hw-prd-dtp-mrs-10-4-47-215,hw-prd-dtp-mrs-10-4-46-28,***********"}, {"servicename": "poseidon-alarm-server", "namespace": "application", "line": "行号:6  invalidOSList: \"CentOS 7.2 64bit,CentOS 8.2 64bit,qianxin-proxy,qianxin-slave,bastionhost_v3.4.27.3_qianxin-v6_x86_64,<PERSON><PERSON>\\"}, {"servicename": "apm-metric-store", "namespace": "application.yml", "line": "行号:10      url: jdbc:TAOS-RS://*********:6041/t3go_driver_action"}, {"servicename": "cockpit-app-api", "namespace": "application.yml", "line": "行号:14        host: 127.0.0.1"}, {"servicename": "cockpit-app-api", "namespace": "application.yml", "line": "行号:40      cuaUrl: http://**********/cua-center-api"}, {"servicename": "cockpit-app-api", "namespace": "application.yml", "line": "行号:43      deviceUrl: http://**********/risk-control-anti-multi-api/result/account"}, {"servicename": "cockpit-web-api", "namespace": "application.yml", "line": "行号:4      cuaUrl: http://**********/cua-center-api/"}, {"servicename": "cockpit-web-api", "namespace": "application.yml", "line": "行号:16        host: 127.0.0.1"}, {"servicename": "t3-dc-ly-api", "namespace": "application.yml", "line": "行号:17        host: 127.0.0.1"}, {"servicename": "t3-dc-ly-api", "namespace": "application.yml", "line": "行号:32      url: *********************************************************************************************************************************************"}, {"servicename": "t3-dc-ly-api", "namespace": "application.yml", "line": "行号:89    url: http://************:12345"}, {"servicename": "t3-dc-ly-api", "namespace": "application.yml", "line": "行号:95       cuaUrl: http://**********/cua-center-api/"}, {"servicename": "iov-online-status-calc", "namespace": "application", "line": "行号:3  kafka.iov.hosts: ***********:9092,***********:9092,***********:9092"}, {"servicename": "app-monitor-collector", "namespace": "application.yml", "line": "行号:20        host: 127.0.0.1"}, {"servicename": "bigdata-order-finished-agg", "namespace": "application", "line": "行号:4  bigdata.mysql.url: ***************************************************************************************"}, {"servicename": "bigdata-order-finished-agg", "namespace": "application", "line": "行号:7  kudu.master: ***********:7051,***********:7051,***********:7051"}, {"servicename": "t3-dc-orderarea-clustering", "namespace": "application.yml", "line": "行号:12        host: 127.0.0.1"}, {"servicename": "t3-dc-orderarea-clustering", "namespace": "application.yml", "line": "行号:24      url: ****************************************************************************************************************************************************"}, {"servicename": "bigdata-monitor-anomaly-agg", "namespace": "application", "line": "行号:3  kudu.master: ***********:7051,***********:7051,***********:7051"}, {"servicename": "bigdata-monitor-anomaly-agg", "namespace": "application", "line": "行号:6  mysql.url: ***************************************************************************************"}, {"servicename": "bigdata-order-flow-agg", "namespace": "application", "line": "行号:4  bigdata.mysql.url: ***************************************************************************************"}, {"servicename": "bigdata-order-flow-agg", "namespace": "application", "line": "行号:7  kudu.master: ***********:7051,***********:7051,***********:7051"}, {"servicename": "t3-dc-visual", "namespace": "application.yml", "line": "行号:8    address: 0.0.0.0"}, {"servicename": "t3-dc-visual", "namespace": "application.yml", "line": "行号:23        host: 127.0.0.1"}, {"servicename": "t3-dc-visual", "namespace": "application.yml", "line": "行号:49      url: **************************************************************************************************************************************************************************"}, {"servicename": "t3-dc-visual", "namespace": "application.yml", "line": "行号:251            host: *********"}, {"servicename": "t3-admin", "namespace": "application.yml", "line": "行号:41    url: http://************:7001/api/v1/message/sms/send"}, {"servicename": "resource-manager", "namespace": "application", "line": "行号:6  sinan.kafka.bootstrap.servers: **********:9092,**********:9092,**********:9092"}, {"servicename": "resource-manager", "namespace": "application.yml", "line": "行号:112      url: http://*************:9080/snap/omaui/custom/faw/vdb/tsp_in"}, {"servicename": "resource-manager", "namespace": "application.yml", "line": "行号:377    baseUrl: http://**************:38081/contract/a"}, {"servicename": "maintain-biz-handle-center", "namespace": "application.yml", "line": "行号:81      reviewUrl: http://*************:8080/km/review/km_review_main/kmReviewMain.do?method=view&fdId="}, {"servicename": "t3-it-open-api", "namespace": "application.yml", "line": "行号:74        host: 127.0.0.1"}, {"servicename": "t3-it-open-api", "namespace": "application.yml", "line": "行号:85      # Redis数据库索引（默认为0  ************"}, {"servicename": "t3-it-open-api", "namespace": "application.yml", "line": "行号:103      urls: ldaps://*********"}, {"servicename": "t3-it-open-api", "namespace": "application.yml", "line": "行号:127    shrAddr: http://***************:6888"}, {"servicename": "t3-it-open-api", "namespace": "application.yml", "line": "行号:169        kmReviewWebserviceService: http://**********:8080/sys/webservice/kmReviewWebserviceService?wsdl"}, {"servicename": "t3-it-open-api", "namespace": "application.yml", "line": "行号:170        kmReviewListNoteGetWebService: http://**********:8080/sys/webservice/kmReviewListNoteGetWebService?wsdl"}, {"servicename": "t3-it-open-api", "namespace": "application.yml", "line": "行号:171        todo: http://**********:8080/sys/webservice/sysNotifyTodoWebService?wsdl"}, {"servicename": "t3-it-open-api", "namespace": "application.yml", "line": "行号:172        kmReviewNowWS: http://**********:8080/sys/webservice/kmReviewNowWS?wsdl"}, {"servicename": "t3-it-open-api", "namespace": "application.yml", "line": "行号:184    fssc: http://************:8080/fssc #费控系统"}, {"servicename": "t3-it-open-api", "namespace": "application.yml", "line": "行号:212    webserviceHost: http://**********:8080  #服务主机地址"}, {"servicename": "t3-it-open-api", "namespace": "application.yml", "line": "行号:284    url: https://************"}, {"servicename": "kz-data-center-fp", "namespace": "application.yml", "line": "行号:12            url: ************************************************"}, {"servicename": "crm-notify-fp", "namespace": "application.yml", "line": "行号:108    dolphin: http://**********:12345/dolphinscheduler"}, {"servicename": "crm-notify-fp", "namespace": "application.yml", "line": "行号:148    url: http://***********:6002/api/tel"}, {"servicename": "crm-notify-fp", "namespace": "application.yml", "line": "行号:168    xjsUrl: http://**************:9092/pressTask/send"}, {"servicename": "crm-notify-fp", "namespace": "application.yml", "line": "行号:169    xjsBlackUrl: http://**************:9092/pressTask/sendBlackList"}, {"servicename": "crm-notify-fp", "namespace": "application.yml", "line": "行号:170    yqsdUrl: http://************/pressTask/send"}, {"servicename": "crm-notify-fp", "namespace": "application.yml", "line": "行号:171    yqsdBlackUrl: http://************/pressTask/sendBlackList"}, {"servicename": "crm-notify-fp", "namespace": "application.yml", "line": "行号:172    trUrl: http://**************:18088/opengateway/task-aggre/pressTask/send"}, {"servicename": "crm-notify-fp", "namespace": "application.yml", "line": "行号:173    trBlackUrl: http://**************:18088/opengateway/task-aggre/pressTask/sendBlackList"}, {"servicename": "t3-it-finance-fp", "namespace": "application.yml", "line": "行号:65        host: 127.0.0.1"}, {"servicename": "t3-it-finance-fp", "namespace": "application.yml", "line": "行号:76      # Redis数据库索引（默认为0  ************"}, {"servicename": "operation-message-proxy", "namespace": "application.yml", "line": "行号:66      # Redis数据库索引（默认为0  ************"}, {"servicename": "leo-business-fp", "namespace": "application.yml", "line": "行号:10        host: 127.0.0.1"}, {"servicename": "open-charging-api", "namespace": "application.yml", "line": "行号:16        host: 127.0.0.1"}, {"servicename": "open-charging-api", "namespace": "application.yml", "line": "行号:43        server: http://************:20904"}, {"servicename": "leo-web-api", "namespace": "application.yml", "line": "行号:16        host: 127.0.0.1"}, {"servicename": "open-charging-app-api", "namespace": "application.yml", "line": "行号:40        host: 127.0.0.1"}, {"servicename": "libra-web-api", "namespace": "application.yml", "line": "行号:15      # Redis数据库索引（默认为0  ************"}, {"servicename": "libra-web-api", "namespace": "application.yml", "line": "行号:34        host: 127.0.0.1"}, {"servicename": "t3-wisdom", "namespace": "application.yml", "line": "行号:13        host: 127.0.0.1"}, {"servicename": "asset-gateway", "namespace": "application.yml", "line": "行号:8      # Redis数据库索引（默认为0  ************"}, {"servicename": "asset-supplier-api", "namespace": "application.yml", "line": "行号:10      # Redis数据库索引（默认为0  ************"}, {"servicename": "wisdom-config-fp", "namespace": "application.yml", "line": "行号:83    gisUrl: http://**********/gis-api"}, {"servicename": "general-business", "namespace": "application.yml", "line": "行号:45      # Redis数据库索引（默认为0  ************"}, {"servicename": "api-control", "namespace": "application.yml", "line": "行号:43      \"local\": \"http://**************:8080/\""}, {"servicename": "api-control", "namespace": "application.yml", "line": "行号:44      \"dev\": \"http://***********/\""}, {"servicename": "api-control", "namespace": "application.yml", "line": "行号:46      \"pressure\": \"http://***************:443/\""}, {"servicename": "api-control", "namespace": "application.yml", "line": "行号:48      \"prd\": \"http://**********/\""}, {"servicename": "api-manager", "namespace": "application.yml", "line": "行号:43      \"local\": \"http://**************:8080/\""}, {"servicename": "api-manager", "namespace": "application.yml", "line": "行号:44      \"dev\": \"http://***********/\""}, {"servicename": "api-manager", "namespace": "application.yml", "line": "行号:46      \"pressure\": \"http://***************:443/\""}, {"servicename": "api-manager", "namespace": "application.yml", "line": "行号:48      \"prd\": \"http://**********/\""}, {"servicename": "gis-position", "namespace": "application.yml", "line": "行号:77    restApiHost: http://**********"}, {"servicename": "gis-position", "namespace": "application.yml", "line": "行号:79    tsApiHost: http://**********"}, {"servicename": "marketingai-amap-api", "namespace": "dcapp.marketamap.yaml", "line": "行号:22    host: 127.0.0.1"}, {"servicename": "marketingai-api", "namespace": "dcapp.market-application.yaml", "line": "行号:23    host: 127.0.0.1"}, {"servicename": "t3-cc-web-api", "namespace": "application.yml", "line": "行号:108      whiteList: http://*************:12301"}, {"servicename": "gpt-core", "namespace": "gpt-core.yaml", "line": "行号:3    host: **********"}, {"servicename": "gpt-core", "namespace": "gpt-core.yaml", "line": "行号:8    host: **********"}, {"servicename": "gpt-core", "namespace": "gpt-core.yaml", "line": "行号:23    url: http://127.0.0.1:7001/gptcc/dialogue-analyze/text"}, {"servicename": "gpt-core", "namespace": "gpt-core.yaml", "line": "行号:26    url: http://127.0.0.1:7002/gptcc/faq"}, {"servicename": "gpt-core", "namespace": "gpt-core.yaml", "line": "行号:33    host: ***********"}, {"servicename": "gpt-core", "namespace": "gpt-core.yaml", "line": "行号:41    - ********* "}, {"servicename": "gpt-core", "namespace": "gpt-core.yaml", "line": "行号:81    host: **********"}, {"servicename": "gis-gateway-api", "namespace": "application.yml", "line": "行号:117      privateIpRegex: '^(221\\.226\\.|221\\.6\\.47\\.187|127.0.0.1|0:0:0:0:0:0:0:1)'"}, {"servicename": "cc-online-web-api-starter", "namespace": "application.yml", "line": "行号:211          base: http://*************:8123"}, {"servicename": "cc-online-web-api-starter", "namespace": "application.yml", "line": "行号:221          base: http://***************:7000"}, {"servicename": "cc-knowledge-web-api-starter", "namespace": "application.yml", "line": "行号:57        host: 127.0.0.1"}, {"servicename": "cc-outbound-web-api-starter", "namespace": "application.yml", "line": "行号:156      callOutUrl: https://api-${**********:8085/needcheck}.cticloud.cn/interface/${outbound.tiCti2Entity.version}/previewOutcall"}, {"servicename": "cc-outbound-web-api-starter", "namespace": "application.yml", "line": "行号:157      agentCreateAndBindUrl: https://api-${**********:8085/needcheck}.cticloud.cn/interface/${outbound.tiCti2Entity.version}/personnel/createAndBind"}, {"servicename": "cc-outbound-web-api-starter", "namespace": "application.yml", "line": "行号:158      agentCreateUrl: https://api-${**********:8085/needcheck}.cticloud.cn/interface/${outbound.tiCti2Entity.version}/agent/create"}, {"servicename": "cc-outbound-web-api-starter", "namespace": "application.yml", "line": "行号:159      agentGetUrl: https://api-${**********:8085/needcheck}.cticloud.cn/interface/${outbound.tiCti2Entity.version}/agent/get"}, {"servicename": "cc-outbound-web-api-starter", "namespace": "application.yml", "line": "行号:160      recordGetUrl: https://api-${**********:8085/needcheck}.cticloud.cn/interface/${outbound.tiCti2Entity.version}/record/getUrl"}, {"servicename": "cc-outbound-web-api-starter", "namespace": "application.yml", "line": "行号:161      agentQueryUrl: https://api-${**********:8085/needcheck}.cticloud.cn/interface/${outbound.tiCti2Entity.version}/agent/query"}, {"servicename": "cc-outbound-web-api-starter", "namespace": "application.yml", "line": "行号:162      getObCdrUrl: https://api-${**********:8085/needcheck}.cticloud.cn/interface/${outbound.tiCti2Entity.version}/cdr/ob/get"}, {"servicename": "cc-outbound-web-api-starter", "namespace": "application.yml", "line": "行号:163      rmsCheckBlackListUrl: https://api-${**********:8085/needcheck}.cticloud.cn/interface/${outbound.tiCti2Entity.version}/rms/checkBlackList"}, {"servicename": "cc-business-web-api-starter", "namespace": "application.yml", "line": "行号:182    chatgpt: http://*************:8123/gptcc/"}, {"servicename": "gis-map-api", "namespace": "application.yml", "line": "行号:100        passengerAppRule: TX:mobileMatch(\"^(***********|***********|***********)\");TX:(versionGe(\"P_a_2.2.7\",\"P_a_2.2.7.0\",\"P_i_2.2.7\",\"P_i_2.2.7.0\")"}, {"servicename": "gis-map-api", "namespace": "application.yml", "line": "行号:160    privateIpRegex: ^(10\\.|172\\.|192\\.|127.0.0.1|0:0:0:0:0:0:0:1)"}, {"servicename": "dispatch_engine", "namespace": "conf.yaml", "line": "行号:31      - \"*************\""}, {"servicename": "dispatch_engine", "namespace": "nodemgr.yaml", "line": "行号:12        - \"*************:10000\""}, {"servicename": "dispatch_engine", "namespace": "dynamic.yaml", "line": "行号:2  distanceUrl: \"http://**********/gis-dispatch-api/v1/route/distance\""}, {"servicename": "strategy-flink-auto-verify", "namespace": "application", "line": "行号:2  kafka.bigdata.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-auto-verify", "namespace": "application", "line": "行号:5  redis.host: **********"}, {"servicename": "strategy-flink-rt-frost-loading-rate", "namespace": "application", "line": "行号:2  frost.sink.kafka.address: *********:9092,*********:9092,*********:9092"}, {"servicename": "strategy-flink-rt-frost-loading-rate", "namespace": "application", "line": "行号:7  kafka.bigdata.address: *********:9092,*********:9092,*********:9092"}, {"servicename": "strategy-order-dispatch", "namespace": "application.yml", "line": "行号:5        host: 127.0.0.1"}, {"servicename": "风神", "namespace": "application", "line": "行号:14  es.host: *********"}, {"servicename": "风神", "namespace": "application", "line": "行号:21  kudu.fence.alarm.master: ***********:7051,***********:7051,***********:7051"}, {"servicename": "风神", "namespace": "application", "line": "行号:23  kudu.master.car.feature.oneminute: ***********:7051,***********:7051,***********:7051"}, {"servicename": "风神", "namespace": "application", "line": "行号:24  kudu.masterAddr: ***********:7051,***********:7051,***********:7051"}, {"servicename": "lingqu-web-canal", "namespace": "application.yml", "line": "行号:17              host: **********"}, {"servicename": "lingqu-web-canal", "namespace": "application.yml", "line": "行号:28          address: **********:3306"}, {"servicename": "lingqu-web-canal", "namespace": "application.yml", "line": "行号:33          url: ****************************/${spring.datasource.database}?useUnicode=true&characterEncoding=UTF-8&useSSL=false"}, {"servicename": "strategy-biz-api", "namespace": "application.yml", "line": "行号:5        host: 127.0.0.1"}, {"servicename": "dispatch-reposition", "namespace": "conf.yaml", "line": "行号:64    - **********:8100"}, {"servicename": "dispatch-reposition", "namespace": "conf.yaml", "line": "行号:65    - **********:8100"}, {"servicename": "bigdata-hubble", "namespace": "application.yml", "line": "行号:5    address: 0.0.0.0"}, {"servicename": "bigdata-hubble", "namespace": "application.yml", "line": "行号:24        host: *********"}, {"servicename": "bigdata-hubble", "namespace": "application.yml", "line": "行号:53        url: **************************************************************************************************************************************************************************************"}, {"servicename": "bigdata-hubble", "namespace": "application.yml", "line": "行号:86        host: ***********"}, {"servicename": "bigdata-hubble", "namespace": "application.yml", "line": "行号:182    url: ldap://*********:389/"}, {"servicename": "bigdata-hubble", "namespace": "application.yml", "line": "行号:185    #url: ldap://***********:389/"}, {"servicename": "bigdata-hubble", "namespace": "application.yml", "line": "行号:215    #hdfsFs: hdfs://*********:4007"}, {"servicename": "bigdata-hubble", "namespace": "application.yml", "line": "行号:216    hdfsFs: hdfs://**********:4007"}, {"servicename": "bigdata-hubble", "namespace": "application.yml", "line": "行号:242    hiveMetalUrl: thrift://*********:7004"}, {"servicename": "bigdata-hubble", "namespace": "application.yml", "line": "行号:243    bootstrapServers: **********:9092,**********:9092,**********:9092"}, {"servicename": "bigdata-hubble", "namespace": "application.yml", "line": "行号:273    url: http://**********:9000"}, {"servicename": "bigdata-hubble", "namespace": "application.yml", "line": "行号:289      haName1Rpc: *********:4007"}, {"servicename": "bigdata-hubble", "namespace": "application.yml", "line": "行号:291      haName2Rpc: **********:4007"}, {"servicename": "bigdata-hubble", "namespace": "application.yml", "line": "行号:302    url: http://*********:6080/service/public/v2/api/policy"}, {"servicename": "perftest-base", "namespace": "application.yml", "line": "行号:4      bootstrap-servers: ***********:9092,***********:9092,***********:9092,**********:9092,***********:9092,***********:9092"}, {"servicename": "perftest-base", "namespace": "application.yml", "line": "行号:34        host: 127.0.0.1     #*********"}, {"servicename": "perftest-base", "namespace": "application.yml", "line": "行号:97    server-addresses: http://**********:8081/t3-job-admin"}, {"servicename": "perftest-base", "namespace": "application.yml", "line": "行号:118      url: http://**********:9090"}, {"servicename": "perftest-base", "namespace": "application.yml", "line": "行号:143      url: http://***********:80811               # TODO"}, {"servicename": "perftest-base", "namespace": "application.yml", "line": "行号:145      url: http://***********:8009"}, {"servicename": "perftest-base", "namespace": "application.yml", "line": "行号:146      dispatchUrl: http://***********:8010"}, {"servicename": "perftest-base", "namespace": "application.yml", "line": "行号:150      url: http://***********:8004"}, {"servicename": "perftest-base", "namespace": "application.yml", "line": "行号:152      url: http://***********:8022  #运管服务"}, {"servicename": "perftest-base", "namespace": "application.yml", "line": "行号:447      url: 'http://*********:8881/api/pred/order/v1'"}, {"servicename": "perftest-base", "namespace": "application.yml", "line": "行号:449      url: 'http://**********:8045'"}, {"servicename": "perftest-base", "namespace": "application.yml", "line": "行号:456    url: http://**********:6019/push?serviceKey=632AA75B504BE9BDFFBFD03F609AA8DA"}, {"servicename": "perftest-base", "namespace": "aaa.yml", "line": "行号:32        host: 127.0.0.1"}, {"servicename": "perftest-base", "namespace": "aaa.yml", "line": "行号:50      # Redis数据库索引（默认为0  ************"}, {"servicename": "perftest-base", "namespace": "aaa.yml", "line": "行号:144        androidVersion: P_a_1.0.0,P_a_1.0.0.0,P_a_1.0.1,P_a_1.0.2,P_a_1.0.3,P_a_1.0.4,P_a_1.0.5,P_a_1.0.6,P_a_1.0.7,P_a_1.0.8,P_a_1.0.9,P_a_1.0.9.1,P_a_1.0.9.2,P_a_1.0.9.3,P_a_1.0.10,P_a_1.0.10.1,P_a_1.0.10.2,P_a_1.0.10.3,P_a_1.0.10.4,P_a_1.0.11,P_a_1.0.11.4,P_a_1.0.12,P_a_1.0.13,P_a_1.0.14,P_a_1.0.14.5,P_a_1.0.14.6,P_a_1.0.14.7,P_a_1.0.14.8,P_a_1.0.14.9,P_a_1.0.15"}, {"servicename": "perftest-base", "namespace": "aaa.yml", "line": "行号:275  integrated-center-address: http://**********"}, {"servicename": "bigdata-sec-feature-base-info-etl", "namespace": "application", "line": "行号:2  bigdata.kafka.server: *********:9092,*********:9092,*********:9092"}, {"servicename": "bigdata-sec-feature-base-info-etl", "namespace": "application", "line": "行号:3  hbase.zookeeper.quorum: **********:2181,********:2181,*********:2181"}, {"servicename": "bigdata-sinan-server", "namespace": "application.yml", "line": "行号:5    address: 0.0.0.0"}, {"servicename": "bigdata-sinan-server", "namespace": "application.yml", "line": "行号:27        host: 127.0.0.1"}, {"servicename": "big<PERSON>-sinan-manager", "namespace": "application.yml", "line": "行号:5    address: 0.0.0.0"}, {"servicename": "big<PERSON>-sinan-manager", "namespace": "application.yml", "line": "行号:24        host: 127.0.0.1"}, {"servicename": "big<PERSON>-sinan-manager", "namespace": "application.yml", "line": "行号:182      url: ldap://*********:389"}, {"servicename": "big<PERSON>-sinan-manager", "namespace": "application.yml", "line": "行号:195    listUrl: http://**********:12345/dolphinscheduler/ui/#/projects/instance/list/"}, {"servicename": "bigdata-sinan-event", "namespace": "application.yml", "line": "行号:5    address: 0.0.0.0"}, {"servicename": "bigdata-sinan-event", "namespace": "application.yml", "line": "行号:20        host: 127.0.0.1"}, {"servicename": "bigdata-sinan-task", "namespace": "application.yml", "line": "行号:5    address: 0.0.0.0"}, {"servicename": "bigdata-sinan-task", "namespace": "application.yml", "line": "行号:24        host: 127.0.0.1"}, {"servicename": "bigdata-sinan-task", "namespace": "application.yml", "line": "行号:66            url: *********************************************************************************************************************************************************************************"}, {"servicename": "bigdata-sinan-task", "namespace": "application.yml", "line": "行号:86    dsUrl: http://**********:12345"}, {"servicename": "bigdata-sinan-task", "namespace": "application.yml", "line": "行号:87    masterUrl: http://**********:5004"}, {"servicename": "bigdata-sinan-task", "namespace": "application.yml", "line": "行号:88    slaveUrl: http://*********:5004  "}, {"servicename": "bigdata-pay-privilege-allot-cala", "namespace": "application", "line": "行号:2  bigdata.kafka.server: **********:9092"}, {"servicename": "bigdata-pay-privilege-allot-cala", "namespace": "application", "line": "行号:4  hbase.zookeeper.quorum: ***********:2181,***********:2181,***********:2181"}, {"servicename": "enterprise-app-api", "namespace": "application.yml", "line": "行号:31        host: 127.0.0.1"}, {"servicename": "enterprise-app-api", "namespace": "application.yml", "line": "行号:286        url: https://**********:8085/needcheck/dpm-open-api"}, {"servicename": "org-manager-api", "namespace": "application.yml", "line": "行号:7        host: 127.0.0.1"}, {"servicename": "org-external-api", "namespace": "application.yml", "line": "行号:29        host: 127.0.0.1"}, {"servicename": "org-manager-boss", "namespace": "application.yml", "line": "行号:18        host: 127.0.0.1"}, {"servicename": "org-manager", "namespace": "application.yml", "line": "行号:127      huaweiUrl: http://**********:8085/needcheck/boocar/onlinecar/services/onlinecar/callbacks/three/mqs/callback?param="}, {"servicename": "org-manager", "namespace": "application.yml", "line": "行号:130      fengxiangUrl: https://**********:8085/needcheck/fx/thirdApi/t3/syncOrderStatus"}, {"servicename": "org-manager", "namespace": "application.yml", "line": "行号:133      maycurUrl: https://**********:8085/needcheck/api/platform/"}, {"servicename": "org-manager", "namespace": "application.yml", "line": "行号:164    url: http://************:8020/generic/AllCarCallBack/T3Order"}, {"servicename": "org-manager", "namespace": "application.yml", "line": "行号:174    url: http://*************:1001/corpApi/carBooking/supplier/t3/orderCallback"}, {"servicename": "t3-pictor-api", "namespace": "application.yml", "line": "行号:15  consulUrl: 127.0.0.1:8500"}, {"servicename": "open-data-collection-api", "namespace": "application.yml", "line": "行号:5        host: 127.0.0.1"}, {"servicename": "orion-web-api", "namespace": "application.yml", "line": "行号:21        host: 127.0.0.1"}, {"servicename": "marketing-web-api", "namespace": "application.yml", "line": "行号:14        host: 127.0.0.1"}, {"servicename": "orion-app-api", "namespace": "application.yml", "line": "行号:20        host: 127.0.0.1"}, {"servicename": "open-app-api", "namespace": "application.yml", "line": "行号:25        host: 127.0.0.1"}, {"servicename": "marketing", "namespace": "application.yml", "line": "行号:156  callCenterCouponTemplate: http://**********:8085/needcheck/api/couponTemplate/v1/save"}, {"servicename": "marketing-center-fp", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "member-app-api", "namespace": "application.yml", "line": "行号:7        host: 127.0.0.1"}, {"servicename": "mall-center-fp", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "mall-app-api", "namespace": "application.yml", "line": "行号:7        host: 127.0.0.1"}, {"servicename": "common-web-api", "namespace": "application.yml", "line": "行号:16        host: 127.0.0.1"}, {"servicename": "common-web-api", "namespace": "application.yml", "line": "行号:49  downloadfileServer: https://***************:1443"}, {"servicename": "marketing-coupon", "namespace": "application.yml", "line": "行号:187      addOrderUrl: http://*************:8080/api/order"}, {"servicename": "marketing-coupon", "namespace": "application.yml", "line": "行号:188      addOrderShortUrl: *************:8080/api/order"}, {"servicename": "marketing-config", "namespace": "application.yml", "line": "行号:146      download_link: https://testbucket-cqoss.t3go.cn/orion-web-api/1437312985474281510/0-passenger-v2.1.24.0-336-2021-09-13-release-{?}.apk"}, {"servicename": "orion-plugin-fp", "namespace": "application.yml", "line": "行号:62    syncAssistantFsTableUrl: https://**********:8085/needcheck/open-apis/bitable/v1/apps/HPmgb717Oats2Ysb8Wbcw3rUnZd/tables/tbl9tQSTvpmQNW07/records"}, {"servicename": "marketing-api-gateway", "namespace": "application.yml", "line": "行号:7        host: 127.0.0.1"}, {"servicename": "monitor-app-api", "namespace": "application.yml", "line": "行号:25        host: 127.0.0.1"}, {"servicename": "cua-member-api", "namespace": "application.yml", "line": "行号:11        host: 127.0.0.1"}, {"servicename": "orion-operation-center", "namespace": "application.yml", "line": "行号:72      webHookUrl: https://**********:8085/needcheck/open-apis/bot/v2/hook/2e68ecd0-8306-48ed-bcc8-12c0f846ea60"}, {"servicename": "orion-operation-center", "namespace": "application.yml", "line": "行号:77          webHookUrl: https://**********:8085/needcheck/open-apis/bot/v2/hook/fafd62ba-391c-4ee1-ae52-c4a568c9e7e5"}, {"servicename": "orion-operation-center", "namespace": "application.yml", "line": "行号:111      authorizeUrl: https://**********:8085/needcheck/img/201907/12/2b9ad6510729619ada051588fc37f5ef.jpg"}, {"servicename": "open-data-collection-fp", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "open-data-collection-fp", "namespace": "application.yml", "line": "行号:146      url: https://**********:8085/needcheck/outer/wechat/applet/token/1739395976666120"}, {"servicename": "mall-gateway-api", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "pictor-center-fp", "namespace": "application.yml", "line": "行号:14        host: 127.0.0.1"}, {"servicename": "business-message-api", "namespace": "application.yml", "line": "行号:31        host: 127.0.0.1"}, {"servicename": "business-message-dispatch", "namespace": "application.yml", "line": "行号:360  pullAnswerUrl: http://************:65171/altts/T3/callReport?date=min&interval=5"}, {"servicename": "marketing-activity", "namespace": "application.yml", "line": "行号:114      signallingHost: http://*************:8008"}, {"servicename": "passenger-activity-api", "namespace": "application.yml", "line": "行号:29        host: 127.0.0.1"}, {"servicename": "passenger-activity-api", "namespace": "application.yml", "line": "行号:177          host: \"{420100: 'https://gjsmtest.i-xiaoma.com.cn',120100: 'http://**************:18000/healthcode/Health/code/query'}\""}, {"servicename": "marketing-activity-server-starter", "namespace": "application.yml", "line": "行号:129    webHook: https://**********:8085/needcheck/open-apis/bot/v2/hook/a8a9c2e5-e907-4f72-8b8c-6c93a162d249"}, {"servicename": "ad-gateway", "namespace": "application.yml", "line": "行号:13      exclude: **********:8085/needcheck,org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration"}, {"servicename": "ad-gateway", "namespace": "application.yml", "line": "行号:30          server-addr: **********"}, {"servicename": "ad-gateway", "namespace": "application.yml", "line": "行号:32          server-addr: **********"}, {"servicename": "marketing-union-member-admin-starter", "namespace": "application.yml", "line": "行号:7        host: 127.0.0.1"}, {"servicename": "bigdata-marketing-transfer-paxr", "namespace": "application.yml", "line": "行号:4      bootstrap-servers: **********:9092,**********:9092,**********:9092"}, {"servicename": "bigdata-marketing-transfer-paxr", "namespace": "application.yml", "line": "行号:5      oms-bootstrap-servers: **********:9095"}, {"servicename": "bigdata-marketing-transfer-paxr", "namespace": "application.yml", "line": "行号:47    host: *********"}, {"servicename": "bigdata-marketing-transfer-paxr", "namespace": "application.yml", "line": "行号:58      jdbcUrl: jdbc:mysql://*********9:3306/t3_abtest_config?characterEncoding=utf8&autoReconnect=true&zeroDateTimeBehavior=convertToNull&useSSL=false&allowMultiQueries=true&serverTimezone=GMT%2B8"}, {"servicename": "bigdata-marketing-transfer-paxr", "namespace": "application.yml", "line": "行号:67      bootstrap-servers: *********:9092,*********:9092,*********:9092"}, {"servicename": "bigdata-marketing-transfer-paxr", "namespace": "application.yml", "line": "行号:73      host: **********"}, {"servicename": "marketing-mall-consumer-starter", "namespace": "application.yml", "line": "行号:11        host: 127.0.0.1"}, {"servicename": "t3-dc-monitor-exhibition-api", "namespace": "application.yml", "line": "行号:18        host: 127.0.0.1"}, {"servicename": "t3-dc-monitor-exhibition-api", "namespace": "application.yml", "line": "行号:40      # Redis数据库索引（默认为0  ************"}, {"servicename": "t3-dc-dsp-manage", "namespace": "application.yml", "line": "行号:36        host: 127.0.0.1"}, {"servicename": "bigdata-golden-toad", "namespace": "application.yml", "line": "行号:57          url: jdbc:TAOS-RS://**********:6041/t3go_bigdata_log"}, {"servicename": "t3-dc-dsp-robin", "namespace": "application.yml", "line": "行号:42        host: 127.0.0.1"}, {"servicename": "t3-dc-dsp-robin", "namespace": "application.yml", "line": "行号:161        ipList: ***********,**********"}, {"servicename": "t3-dc-dsp-robin", "namespace": "application.yml", "line": "行号:168        nj: http://*************:18085/datadispatch/auth/320102"}, {"servicename": "t3-dc-dsp-robin", "namespace": "application.yml", "line": "行号:190    name-server: ************:9876;************:9876"}, {"servicename": "t3-dc-dsp-robin", "namespace": "application.yml", "line": "行号:200      endpoint: http://**********:8085/needcheck/"}, {"servicename": "abtest-config-admin-starter", "namespace": "application.yml", "line": "行号:11        host: 127.0.0.1"}, {"servicename": "marketing-mall-server-starter", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "marketing-mall-server-starter", "namespace": "application.yml", "line": "行号:137    url: https://**********:8085/needcheck"}, {"servicename": "resource-driver-app-api", "namespace": "application.yml", "line": "行号:11        host: 127.0.0.1"}, {"servicename": "vehicle-app-api", "namespace": "application.yml", "line": "行号:35        host: 127.0.0.1"}, {"servicename": "vehicle-app-api", "namespace": "application.yml", "line": "行号:107      host: '{420100: ''https://gjsmtest.i-xiaoma.com.cn'',120100: ''http://**************:18000/healthcode/Health/code/query''}'"}, {"servicename": "vigen-web-api", "namespace": "application.yml", "line": "行号:12        host: 127.0.0.1"}, {"servicename": "vigen-web-api", "namespace": "application.yml", "line": "行号:25        # Redis数据库索引（默认为0  ************"}, {"servicename": "t3-driver-resume-web", "namespace": "application.yml", "line": "行号:27        host: 127.0.0.1"}, {"servicename": "taxi-driver-app-api", "namespace": "application.yml", "line": "行号:42        host: 127.0.0.1"}, {"servicename": "taxi-driver-app-api", "namespace": "application.yml", "line": "行号:155        appUrl: http://*************:8080/view/2.%E5%8F%B8%E6%9C%BA/job/Driver-android/2937/artifact/T3Driver/driver/build/outputs/apk/release/channel/0-driver-arm64-v8a-v2.8.2.40-1248-2023-09-06-release-log.apk"}, {"servicename": "taxi-h5-api", "namespace": "application.yml", "line": "行号:50      # Redis数据库索引（默认为0  ************"}, {"servicename": "taxi-admin-web-api", "namespace": "application.yml", "line": "行号:43      # Redis数据库索引（默认为0  ************"}, {"servicename": "taxi-admin-web-api", "namespace": "application.yml", "line": "行号:64        host: 127.0.0.1"}, {"servicename": "driver-resume", "namespace": "application.yml", "line": "行号:5        host: 127.0.0.1"}, {"servicename": "driver-resume", "namespace": "application.yml", "line": "行号:109        bootstrap-servers: **********:9092,**********:9092,**********:9092"}, {"servicename": "driver-resume", "namespace": "application.yml", "line": "行号:285      httpAddress: http://127.0.0.1:20502/"}, {"servicename": "driver-resume", "namespace": "application-ext.yml", "line": "行号:210      ip: http://*************"}, {"servicename": "driver-resume", "namespace": "application-ext.yml", "line": "行号:321      ip: http://106.15.207.8011"}, {"servicename": "driver-resume", "namespace": "application-ext.yml", "line": "行号:329    jdbcUrl: jdbc:TAOS-RS://*********:6041/t3_travel_driver?connectTimeout=2000&socketTimeout=2000"}, {"servicename": "driver-resume", "namespace": "application-ext.yml", "line": "行号:339      jdbcUrl: jdbc:TAOS-RS://*********:6041/t3_travel_driver?connectTimeout=2000&socketTimeout=2000"}, {"servicename": "taxi-common-fp", "namespace": "application.yml", "line": "行号:7        host: 127.0.0.1"}, {"servicename": "salary-web-api", "namespace": "application.yml", "line": "行号:11        host: 127.0.0.1"}, {"servicename": "resource-center-api", "namespace": "application.yml", "line": "行号:11        host: 127.0.0.1"}, {"servicename": "taxi-dashboard-fp", "namespace": "application.yml", "line": "行号:28      # Redis数据库索引（默认为0  ************"}, {"servicename": "t3-dc-monitor-taxi-api", "namespace": "application.yml", "line": "行号:79    url: http://************"}, {"servicename": "t3-dc-monitor-taxi-api", "namespace": "application.yml", "line": "行号:102    #file-token: http://***********"}, {"servicename": "t3-dc-monitor-taxi-api", "namespace": "application.yml", "line": "行号:104    #resource2: http://***********:8000"}, {"servicename": "taxi-dashboard-web-api", "namespace": "application.yml", "line": "行号:48        host: 127.0.0.1"}, {"servicename": "taxi-dashboard-web-api", "namespace": "application.yml", "line": "行号:90  openplatform: http://**********:8085/needcheck/open-api"}, {"servicename": "taxi-dashboard-web-api", "namespace": "application.yml", "line": "行号:94  uploadfileServer: http://**********:8085/needcheck"}, {"servicename": "taxi-dashboard-web-api", "namespace": "application.yml", "line": "行号:95  downloadfileServer: http://**********:8085/needcheck"}, {"servicename": "taxi-dashboard-web-api", "namespace": "application.yml", "line": "行号:96  middleWare: http://**********:8085/needcheck"}, {"servicename": "salary-center-fp", "namespace": "application.yml", "line": "行号:95    task-gateway: http://************:6008"}, {"servicename": "salary-center-fp", "namespace": "application.yml", "line": "行号:117        host: *********"}, {"servicename": "taxi-applet-api", "namespace": "application.yml", "line": "行号:51      # Redis数据库索引（默认为0  ************"}, {"servicename": "resource-recruit-api", "namespace": "application.yml", "line": "行号:11        host: 127.0.0.1"}, {"servicename": "taxi-task", "namespace": "application.yml", "line": "行号:32      # Redis数据库索引（默认为0,实际为94 ) ************"}, {"servicename": "resource-secret", "namespace": "application.yml", "line": "行号:12        host: 127.0.0.1"}, {"servicename": "driver-school", "namespace": "application.yml", "line": "行号:55        host: 127.0.0.1"}, {"servicename": "driver-compliance-fp", "namespace": "application.yml", "line": "行号:72        host: 127.0.0.1"}, {"servicename": "t3-dc-datamap", "namespace": "application.yml", "line": "行号:5    address: 0.0.0.0"}, {"servicename": "t3-dc-datamap", "namespace": "application.yml", "line": "行号:17        host: 127.0.0.1"}, {"servicename": "t3-dc-datamap", "namespace": "application.yml", "line": "行号:66        url: jdbc:trino://*********:9000/hive/default"}, {"servicename": "t3-dc-datamap", "namespace": "application.yml", "line": "行号:102        address: **********"}, {"servicename": "t3-dc-datamap", "namespace": "application.yml", "line": "行号:204      url: http://*********:6080/service"}, {"servicename": "driver-app-api", "namespace": "application.yml", "line": "行号:40        host: 127.0.0.1"}, {"servicename": "operation-gateway", "namespace": "application.yml", "line": "行号:19        host: 127.0.0.1"}, {"servicename": "driver-flink-binMix", "namespace": "application", "line": "行号:5  t3.doris.address: ***********:8030"}, {"servicename": "driver-flink-binMix", "namespace": "application", "line": "行号:7  t3.doris.jdbc.url: jdbc:mysql://**********:9030"}, {"servicename": "driver-flink-binMix", "namespace": "application", "line": "行号:11  t3.driver.biz.kafka.server: **********:9092;**********:9092;**********:9092"}, {"servicename": "driver-flink-binMix", "namespace": "application", "line": "行号:14  t3.driver.biz.mq.server: **********"}, {"servicename": "driver-flink-binMix", "namespace": "application", "line": "行号:16  t3.driver.lingqu.kafka.server: **********:9095"}, {"servicename": "driver-flink-binMix", "namespace": "application", "line": "行号:27  t3.redis.host: **********"}, {"servicename": "travel-manager", "namespace": "application.yml", "line": "行号:56  sipServerUrl: http://**********:8085/needcheck:8081/v1/manager"}, {"servicename": "operation-analysis", "namespace": "application.yml", "line": "行号:304    clientips: **************"}, {"servicename": "driver-flink-hours", "namespace": "application", "line": "行号:2  bigdata.kafka.server: **********:9093,**********:9094,**********:9095"}, {"servicename": "driver-flink-hours", "namespace": "application", "line": "行号:4  t3.dispatch.biz.kafka.server: **********:9092,**********:9092,**********:9092"}, {"servicename": "driver-flink-hours", "namespace": "application", "line": "行号:5  t3.tdengine.address: **********:6041"}, {"servicename": "operation-job", "namespace": "application.yml", "line": "行号:42      # Redis数据库索引（默认为0  ************"}, {"servicename": "task-compute-fp", "namespace": "application.yml", "line": "行号:22          #bootstrap-servers: *********:9092,*********:9092,*********:9092"}, {"servicename": "task-manage-fp", "namespace": "application.yml", "line": "行号:50          #bootstrap-servers: *********:9092,*********:9092,*********:9092"}, {"servicename": "task-manage-fp", "namespace": "application.yml", "line": "行号:184      url: http://**********:12345"}, {"servicename": "task-manage-fp", "namespace": "application.yml", "line": "行号:298      logUrl: http://dolphin-log-test.t3go.com.cn/exception/discover?url=http%3A%2F%2F10.6.2.83%3A5601%2Fapp%2Fkibana%23%2Fdiscover%3Fembed%3Dtrue"}, {"servicename": "task-manage-api", "namespace": "application.yml", "line": "行号:33        host: 127.0.0.1"}, {"servicename": "operation-admin-api", "namespace": "application.yml", "line": "行号:10      # Redis数据库索引（默认为0  ************"}, {"servicename": "operation-admin-api", "namespace": "application.yml", "line": "行号:29        host: 127.0.0.1"}, {"servicename": "operation-admin-api", "namespace": "application.yml", "line": "行号:89    url: http://************:7001/api/v1/message/sms/send"}, {"servicename": "task-handle-fp", "namespace": "application.yml", "line": "行号:18        bootstrap-servers: **********:9095"}, {"servicename": "driver-flink-task", "namespace": "application", "line": "行号:6  dolphin.secretKeys.task_msg_record.host: *********"}, {"servicename": "driver-flink-task", "namespace": "application", "line": "行号:13  kafka.bootstrap-servers: *********:9092;*********:9092;*********:9092"}, {"servicename": "driver-flink-task", "namespace": "application", "line": "行号:15  t3.doris.address: ***********:8030"}, {"servicename": "driver-flink-task", "namespace": "application", "line": "行号:17  t3.doris.jdbc.url: jdbc:mysql://**********:9030"}, {"servicename": "driver-flink-task", "namespace": "application", "line": "行号:20  t3.rocketmq.address: **********:8100;**********:8100"}, {"servicename": "travel-data-center", "namespace": "application.yml", "line": "行号:33      bootstrap-servers: **********:9093,**********:9094,**********:9095"}, {"servicename": "travel-data-center", "namespace": "application.yml", "line": "行号:116    address: *********:6041"}, {"servicename": "travel-data-center", "namespace": "application.yml", "line": "行号:118    jdbcUrl: jdbc:TAOS-RS://**********:6041/t3_travel_driver?connectTimeout=10000&socketTimeout=10000"}, {"servicename": "travel-data-center", "namespace": "application.yml", "line": "行号:127      jdbcUrl: jdbc:TAOS-RS://**********:6041?connectTimeout=10000&socketTimeout=10000"}, {"servicename": "travel-data-center", "namespace": "application.yml", "line": "行号:158      bootstrap-servers: *********:6041"}, {"servicename": "sync-settlement-es", "namespace": "application", "line": "行号:7  kafka.zk.hosts.route: **********:9095"}, {"servicename": "sync-settlement-es", "namespace": "application", "line": "行号:8  paySettlementEs.host: **********"}, {"servicename": "sync-settlement-es", "namespace": "application", "line": "行号:12  paySettlementHbase.hbase.zookeeper.quorum: **********:2181,********:2181,*********:2181"}, {"servicename": "finance-center", "namespace": "application.yml", "line": "行号:126      eas-url: http://***************:6888"}, {"servicename": "settlement-center", "namespace": "application.yml", "line": "行号:274      webhookUrl: https://**********:8085/needcheck/open-apis/bot/v2/hook/461353d9-58a4-4ae6-997e-7f544af178a2"}, {"servicename": "finance-api", "namespace": "application.yml", "line": "行号:22        host: 127.0.0.1"}, {"servicename": "cua-certificate", "namespace": "application.yml", "line": "行号:76    uploadFileUrl: http://*********:8000"}, {"servicename": "cua-certificate", "namespace": "application.yml", "line": "行号:128      url: https%3A%2F%**********:8085/needcheck%2Fwebapp%2Fcw%2Fcar%2Fisd%2FHome.html%3Fapptype%3DISD_C_CW_MAIN%26channelid%3D235814%26allianceid%3D3779004%26sid%3D20445360%26isHideHomeBack%3Dtr"}, {"servicename": "cua-certificate", "namespace": "application.yml", "line": "行号:131        url: https%3A%2F%**********:8085/needcheck%2Ftangram%2FMTgzNzc%3D%3Fctm_ref%3Dvactang_page_18377%26isHideNavBar%3DYES%26apppgid%3D10320669188%26allianceid%3D3779004%26sid%3D20981229"}, {"servicename": "cua-certificate", "namespace": "application.yml", "line": "行号:144    url: http://**********:8085/needcheck/api_v3/"}, {"servicename": "cua-certificate", "namespace": "application.yml", "line": "行号:157    getUserInfoUrl: https://**********:8085/needcheck/oauth/userinfo.do"}, {"servicename": "cua-certificate", "namespace": "application.yml", "line": "行号:158    seniorurl: https://**********:8085/needcheck/oauth/applet_seniorauth.do"}, {"servicename": "pay-center-api", "namespace": "application.yml", "line": "行号:7        host: 127.0.0.1"}, {"servicename": "pay-internal-api", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "driver-core-app-api", "namespace": "application.yml", "line": "行号:38        host: 127.0.0.1"}, {"servicename": "driver-core-app-api", "namespace": "application.yml", "line": "行号:145    #task: http://************:6008"}, {"servicename": "pay-bigdata-center", "namespace": "application.yml", "line": "行号:200          bootstrap-servers: **********:9095"}, {"servicename": "invoice-core-center", "namespace": "application.yml", "line": "行号:54      invoiceUrl: http://**************:8091/stms/openapi/allElectricInfo1"}, {"servicename": "invoice-core-center", "namespace": "application.yml", "line": "行号:55      redInvoiceUrl: http://**************:8091/stms/openapi/nt-zj/redbillreceive1"}, {"servicename": "invoice-core-center", "namespace": "application.yml", "line": "行号:56      redInvoiceQueryUrl: http://**************:8091/stms/openapi/common/commonapi1"}, {"servicename": "invoice-core-center", "namespace": "application.yml", "line": "行号:62      invoiceUrl: http://**************:18911/apiservice/api"}, {"servicename": "finance-web-api", "namespace": "application.yml", "line": "行号:16        host: 127.0.0.1"}, {"servicename": "driver-task-bugdet", "namespace": "application.yml", "line": "行号:16        host: 127.0.0.1"}, {"servicename": "callback-api", "namespace": "application.yml", "line": "行号:7        host: 127.0.0.1"}, {"servicename": "cua-center-api", "namespace": "application.yml", "line": "行号:137      webhook-url: https://**********:8085/needcheck/open-apis/bot/v2/hook/4f67a36c-a286-4b96-8df2-0fc3a22204db"}, {"servicename": "adapter-center", "namespace": "application", "line": "行号:36  baorong-config.backTicketQueryUrl: http://***************:8854/interfaceApi/refundQry"}, {"servicename": "adapter-center", "namespace": "application", "line": "行号:37  baorong-config.bankTransactionsUrl: http://***************:8854/interfaceApi/queryBanktransaction"}, {"servicename": "adapter-center", "namespace": "application", "line": "行号:39  baorong-config.payReceiptUrl: http://***************:8854/interfaceApi/settlement/electronicUrl"}, {"servicename": "adapter-center", "namespace": "application", "line": "行号:40  baorong-config.payStatusQueryUrl: http://***************:8854/interfaceApi/dsbrQry"}, {"servicename": "adapter-center", "namespace": "application", "line": "行号:41  baorong-config.payUrl: http://***************:8854/interfaceApi/dsbrSbmt"}, {"servicename": "adapter-center", "namespace": "application", "line": "行号:42  baorong-config.receiptQueryUrl: http://***************:8854/interfaceApi/getReceiptUrlByUrid"}, {"servicename": "adapter-center", "namespace": "application.yml", "line": "行号:163    backUrl: http://***************:8080/ACPSample_WuTiaoZhuan_Token/backRcvResponse"}, {"servicename": "adapter-center", "namespace": "application.yml", "line": "行号:209    cibProxyUrl: http://**************:8007"}, {"servicename": "adapter-center", "namespace": "application.yml", "line": "行号:210    cibUrl: https://**************:8024/firmbank/online/PFCFoxSecurities"}, {"servicename": "cua-user-api-c", "namespace": "application.yml", "line": "行号:23        host: 127.0.0.1"}, {"servicename": "cua-user-api-c", "namespace": "application.yml", "line": "行号:76        androidVersion: P_a_1.0.0,P_a_1.0.0.0,P_a_1.0.1,P_a_1.0.2,P_a_1.0.3,P_a_1.0.4,P_a_1.0.5,P_a_1.0.6,P_a_1.0.7,P_a_1.0.8,P_a_1.0.9,P_a_1.0.9.1,P_a_1.0.9.2,P_a_1.0.9.3,P_a_1.0.10,P_a_1.0.10.1,P_a_1.0.10.2,P_a_1.0.10.3,P_a_1.0.10.4,P_a_1.0.11,P_a_1.0.11.4,P_a_1.0.12,P_a_1.0.13,P_a_1.0.14,P_a_1.0.14.5,P_a_1.0.14.6,P_a_1.0.14.7,P_a_1.0.14.8,P_a_1.0.14.9,P_a_1.0.15"}, {"servicename": "risk-streaming", "namespace": "application", "line": "行号:2  kafka.biz.hosts: *********:9092,*********:9092,*********:9092"}, {"servicename": "risk-streaming", "namespace": "application", "line": "行号:3  kafka.hosts: *********:9092,*********:9092,*********:9092"}, {"servicename": "risk-streaming", "namespace": "application", "line": "行号:4  kafka.iov.hosts: **********:9092,**********:9092,********:9092"}, {"servicename": "risk-vehicle-miles-alarm", "namespace": "application", "line": "行号:2  kafka.biz.hosts: *********:9092,*********:9092,*********:9092"}, {"servicename": "risk-vehicle-miles-alarm", "namespace": "application", "line": "行号:3  kafka.hosts: *********:9092,*********:9092,*********:9092"}, {"servicename": "risk-vehicle-miles-alarm", "namespace": "application", "line": "行号:4  kafka.iov.hosts: **********:9092,**********:9092,********:9092"}, {"servicename": "risk-vehicle-miles-alarm", "namespace": "application", "line": "行号:15  slb.url.push: http://**********:8058"}, {"servicename": "driver-caring", "namespace": "application.yml", "line": "行号:167    verifyServer: http://127.0.0.1:50880"}, {"servicename": "driver-caring", "namespace": "application.yml", "line": "行号:371          callback-url: http://127.0.0.1:50880/finance/verify/notify"}, {"servicename": "risk-control-center", "namespace": "application.yml", "line": "行号:25      bootstrap-servers-binlog: **********:9095"}, {"servicename": "risk-control-center", "namespace": "application.yml", "line": "行号:141    predict: http://************:8502/predict"}, {"servicename": "risk-control-center", "namespace": "application.yml", "line": "行号:163    url: http://**********:8088/ctu/event.do"}, {"servicename": "risk-control-center", "namespace": "application.yml", "line": "行号:167      url: http://**********:8088/ctu/event.do"}, {"servicename": "cua-user-tenant", "namespace": "application.yml", "line": "行号:159    uploadFileUrl: http://*********:8000"}, {"servicename": "cua-user-tenant", "namespace": "application.yml", "line": "行号:164    url: https://**********:8085/needcheck/robot/send?access_token=4bc8d735549b3759c952baa2c13598863b66b4e35a8dcf367ae020ebc441e032"}, {"servicename": "risk-task-service", "namespace": "application.yml", "line": "行号:91      url: http://**********:8088/udid/api/getDeviceInfo"}, {"servicename": "risk-store-service", "namespace": "application.yml", "line": "行号:55      bootstrap-servers-binlog: **********:9095"}, {"servicename": "risk-store-service", "namespace": "application.yml", "line": "行号:176    secMonitorUrl: https://**********:8085/needcheck/open-apis/bot/v2/hook/8188907e-84ce-4235-90c3-15835cfb0384"}, {"servicename": "risk-store-service", "namespace": "application.yml", "line": "行号:177    riskMonitorUrl: https://**********:8085/needcheck/open-apis/bot/v2/hook/676a800d-aeb5-4066-b0a2-0e1692fcb562"}, {"servicename": "risk-store-service", "namespace": "application.yml", "line": "行号:178    pretrialMonitorUrl: https://**********:8085/needcheck/open-apis/bot/v2/hook/b67af7d4-a908-4843-ab8e-3696f64dec9e"}, {"servicename": "risk-control-anti-multi-api", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "risk-nearlyline-service", "namespace": "application.yml", "line": "行号:25      bootstrap-servers-binlog: **********:9095"}, {"servicename": "risk-nearlyline-service", "namespace": "application.yml", "line": "行号:144    url: http://**********:8088/ctu/event.do"}, {"servicename": "safety-web-api", "namespace": "application.yml", "line": "行号:15        host: 127.0.0.1"}, {"servicename": "risk-account-manager", "namespace": "application.yml", "line": "行号:41      bootstrap-servers-offline: kafka-bigdata-1-c-t3.t3go.com.cn:9092,kafka-bigdata-2-c-t3.t3go.com.cn:9092,kafka-bigdata-3-c-t3.t3go.com.cn:9092,**********:9095"}, {"servicename": "risk-account-api", "namespace": "application.yml", "line": "行号:25        host: 127.0.0.1"}, {"servicename": "risk-account-api", "namespace": "application.yml", "line": "行号:78    predict: http://************:8507/predict"}, {"servicename": "risk-account-api", "namespace": "application.yml", "line": "行号:79    modelCall: http://***********:80/seldon/ns-riskmg-space/riskmg-takepay-spam/api/v1.0/predictions"}, {"servicename": "risk-account-api", "namespace": "application.yml", "line": "行号:83      modelCall: http://**********:8588/predict"}, {"servicename": "risk-account-api", "namespace": "application.yml", "line": "行号:84    secondModelCall: http://***********/seldon/mountain-service-test/takepay-white-model-fuxiang-riskmg/api/v1.0/predictions"}, {"servicename": "risk-account-api", "namespace": "application.yml", "line": "行号:88    arriveDestModelCall: http://***********/seldon/mountain-service-test/fast-order-model-fuxiang-riskmg/api/v1.0/predictions"}, {"servicename": "risk-account-api", "namespace": "application.yml", "line": "行号:90    oldUserModelCall: http://10.16.0.70:80/seldon/ns-bigdata-test/old-passenger-risk-model/api/v1.0/predictions"}, {"servicename": "risk-account-api", "namespace": "application.yml", "line": "行号:92    newDingHeiModelCall: http://***********/seldon/mountain-service-test/riskmg-takepay-spam-v2-fuxiang-riskmg/api/v1.0/predictions"}, {"servicename": "risk-account-api", "namespace": "application.yml", "line": "行号:98    url: http://**********:8088/ctu/event.do"}, {"servicename": "risk-account-api", "namespace": "application.yml", "line": "行号:103      url: http://**********:8088/ctu/event.do"}, {"servicename": "risk-feature-service", "namespace": "application.yml", "line": "行号:34      bootstrap-servers-lingqu: **********:9095"}, {"servicename": "sec-app-api", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "risk-device-finger-api", "namespace": "application.yml", "line": "行号:12        host: 127.0.0.1"}, {"servicename": "sec-flink-route-point-stay", "namespace": "application", "line": "行号:2  es.host: *********"}, {"servicename": "sec-flink-route-point-stay", "namespace": "application", "line": "行号:6  hbaseZookeeperQuorum: **********:2181,**********:2181,*********:2181"}, {"servicename": "sec-flink-route-point-stay", "namespace": "application", "line": "行号:8  kafka.hosts: *********:9092,*********:9092,*********:9092"}, {"servicename": "sec-flink-route-point-stay", "namespace": "application", "line": "行号:11  mysql.url: ********************************************************************************************************"}, {"servicename": "strategy-flink-order-count-calc", "namespace": "application", "line": "行号:2  kafka.broadcast.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-order-count-calc", "namespace": "application", "line": "行号:5  kafka.route.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-order-count-calc", "namespace": "application", "line": "行号:8  kafka.sink.orderIndex.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-grid-order-fare", "namespace": "application", "line": "行号:2  kafka.route.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-grid-order-fare", "namespace": "application", "line": "行号:5  kafka.sink.orderIndex.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "sp-sf-api", "namespace": "application.yml", "line": "行号:203      ip: http://106.15.207.8011"}, {"servicename": "ccp-autodrive-operation-manage", "namespace": "application.yml", "line": "行号:12        host: 127.0.0.1"}, {"servicename": "recruit-rpa-external", "namespace": "application.yml", "line": "行号:137    url: http://**********:12345/driver-recruitment/llm-chat/chat-with-openai"}, {"servicename": "recruit-rpa-external", "namespace": "application.yml", "line": "行号:138    synUrl: http://**********:12345/driver-recruitment/llm-chat/simple-chat"}, {"servicename": "recruit-rpa-external", "namespace": "application.yml", "line": "行号:139    syncUrl: http://**********:9999/rpa-message/forward"}, {"servicename": "recruit-rpa-external", "namespace": "application.yml", "line": "行号:140    confirmMsg: http://**********:12345/driver-recruitment/llm-chat/response-callback"}, {"servicename": "strategy-flink-order-index-calc", "namespace": "application", "line": "行号:2  kafka.average.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-order-index-calc", "namespace": "application", "line": "行号:5  kafka.route.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-order-index-calc", "namespace": "application", "line": "行号:8  kafka.sink.orderIndex.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "abtest-data", "namespace": "application.yml", "line": "行号:12        host: 127.0.0.1"}, {"servicename": "abtest-diversion", "namespace": "application.yml", "line": "行号:12        host: 127.0.0.1"}, {"servicename": "driver-task-manage", "namespace": "application.yml", "line": "行号:49          #bootstrap-servers: *********:9092,*********:9092,*********:9092"}, {"servicename": "strategy-flink-driver-reward", "namespace": "application", "line": "行号:5  allRailTask.url: http://**********:80/operation-gateway/v1/driver/task/queryRainPage"}, {"servicename": "strategy-flink-driver-reward", "namespace": "application", "line": "行号:6  business.kafka.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-driver-reward", "namespace": "application", "line": "行号:13  confirm.rate.url: http://**********:80/strategy-biz-api/metric/confirmRateQuery"}, {"servicename": "strategy-flink-driver-reward", "namespace": "application", "line": "行号:15  driver.task.url: http://**********:80/operation-gateway/v1/driver/task/queryRainTaskByCity"}, {"servicename": "strategy-flink-driver-reward", "namespace": "application", "line": "行号:20  pointsInCondition.url: http://**********:80/gis-api/v2/fence/points-in-condition"}, {"servicename": "strategy-flink-driver-reward", "namespace": "application", "line": "行号:23  railTaskByDriver.url: http://**********:80/operation-gateway/v1/driver/task/queryRainByDriver"}, {"servicename": "strategy-flink-driver-reward", "namespace": "application", "line": "行号:25  railTaskByDriverAndTask.url: http://**********:80/operation-gateway/v1/driver/task/queryRainByDriverAndTaskIds"}, {"servicename": "strategy-flink-driver-reward", "namespace": "application", "line": "行号:27  rainTaskNeedIndex.url: http://**********:80/strategy-biz-api/metric/getRainTaskNeedIndex"}, {"servicename": "strategy-flink-driver-reward", "namespace": "application", "line": "行号:28  route.kafka.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-driver-reward", "namespace": "application", "line": "行号:31  sink.huid.kafka.address: **********:9092,*********:9092,**********:9092"}, {"servicename": "strategy-flink-driver-reward", "namespace": "application", "line": "行号:33  sink.kafka.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-driver-reward", "namespace": "application", "line": "行号:36  sliceOrderCount.url: http://**********:80/strategy-biz-api/metric/getSliceOrderCount"}, {"servicename": "strategy-flink-driver-reward", "namespace": "application", "line": "行号:39  supply.demand.url: http://**********:80/strategy-biz-api/metric/getCitySupplyDemand"}, {"servicename": "strategy-flink-driver-reward", "namespace": "application", "line": "行号:41  taskAccumulation.url: http://**********:80/strategy-biz-api/task/getTaskAccumulation"}, {"servicename": "ccp-chat", "namespace": "application.yml", "line": "行号:30        host: 127.0.0.1"}, {"servicename": "ccp-autodrive-backend-api", "namespace": "application.yml", "line": "行号:19        host: 127.0.0.1"}, {"servicename": "ccp-autodrive-backend-api", "namespace": "application.yml", "line": "行号:50      url: http://***************:8080/ccp-vehicle-simulator/vehicle_task/add"}, {"servicename": "ccp-autodrive-gov-monitor", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "ccp-autodrive-monitor-data", "namespace": "application.yml", "line": "行号:11        host: 127.0.0.1"}, {"servicename": "route-flink", "namespace": "application", "line": "行号:7  hbase.zk.hosts.passenger: **********:2181,********:2181,*********:2181"}, {"servicename": "route-flink", "namespace": "application", "line": "行号:8  hbase.zk.hosts.route: **********:2181,********:2181,*********:2181"}, {"servicename": "route-flink", "namespace": "application", "line": "行号:9  kafka.zk.hosts.route: **********:9095"}, {"servicename": "route-flink", "namespace": "application", "line": "行号:19  mysql.url: *****************************************************************************"}, {"servicename": "ccp-autodrive-base-data", "namespace": "application.yml", "line": "行号:11        host: 127.0.0.1"}, {"servicename": "ccp-iot-gateway", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "ccp-iot-gateway", "namespace": "application.yml", "line": "行号:103    url: https://**********:8085/needcheck"}, {"servicename": "ccp-autodrive-cloud-control", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "ccp-iot-dns", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "ccp-autodrive-monitor-process", "namespace": "application.yml", "line": "行号:11        host: 127.0.0.1"}, {"servicename": "ccp-autodrive-frontend-api", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "traffic-copy", "namespace": "application.yml", "line": "行号:7        host: 127.0.0.1"}, {"servicename": "traffic-copy", "namespace": "application.yml", "line": "行号:38        url: ********************************,*********:8123,*********:8123/t3_traffic"}, {"servicename": "traffic-copy", "namespace": "application.yml", "line": "行号:57      bootstrap-servers: **********:9092,**********:9092,**********:9092"}, {"servicename": "sp-growth-api", "namespace": "application.yml", "line": "行号:15        host: 127.0.0.1"}, {"servicename": "strategy-flink-order-transport-index-new-m", "namespace": "application", "line": "行号:5  bigdata.kafka.server: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-order-transport-index-new-m", "namespace": "application", "line": "行号:6  business.kafka.server: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:14  bigdata.es.host: **********"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:17  bigdata.kafka.server: **********:9092,*********:9092,**********:9092"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:18  bigdata.tx.kafka.server: **********:9092"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:19  business.kafka.server: **********:9092,**********:9092,*********:9092"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:26  clickhouse.jdbc.url: *********************************"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:29  core.hbase.zookeeper.quorum: ************,************,************"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:38  es.host: **********"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:44  four.clickhouse.jdbc.url: *********************************"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:62  hbase.zookeeper.quorum: **********,**********,*********"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:66  hw.hbase.zookeeper.quorum: **********,**********,*********"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:67  iov.kafka.server: *********:9092,**********:9092,*********:9092"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:69  iov.mysql.url: ***************************************************************************************************************************************************"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:75  log.kafka.server: 10.6.3.40:9092,10.6.4.194:9092,10.6.2.1:9092"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:77  mysql.jdbc.url: ***************************************************************************************************************************************************"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:78  mysql.log.url: ********************************/"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:88  one.clickhouse.jdbc.url: *********************************"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:91  operation.mysql.url: ***************************************************************************************************************************************************"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:111  presto.jdbc.url: **********************************"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:113  redis.bigdata.host: **********"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:117  redis.host: ***********"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:130  sec.hbase.zookeeper.quorum: **********,**********,*********"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:138  supply.redis.host: **********"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:142  three.clickhouse.jdbc.url: *********************************"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:173  trino.jdbc.url: jdbc:trino://*********:9000/hive/default"}, {"servicename": "strategy-flink-ads-dri-online-duration-calc", "namespace": "application", "line": "行号:176  two.clickhouse.jdbc.url: *********************************"}, {"servicename": "partner-workorder-fp", "namespace": "application.yml", "line": "行号:77        host: 127.0.0.1"}, {"servicename": "partner-workorder-fp", "namespace": "application.yml", "line": "行号:259      check-url: http://**************:8080/"}, {"servicename": "partner-workorder-fp", "namespace": "application-1.yml", "line": "行号:306      alarmUrl: https://**********:8085/needcheck/robot/send?access_token=eac56d8d98e087f55a809549801d1590a741fd607c79e9887f74eeae51d4fd5a"}, {"servicename": "strategy-flink-ord-supply-feature", "namespace": "application", "line": "行号:2  bigdata.kafka.server: **********:9092,**********:9092,**********:9092"}, {"servicename": "lens", "namespace": "application", "line": "行号:2  consulAddr: 127.0.0.1:8500"}, {"servicename": "driver-flink-monitor", "namespace": "application.yml", "line": "行号:2  bigdata.kafka.server: **********:9092"}, {"servicename": "strategy-flink-order-push-confirm-rate", "namespace": "application", "line": "行号:2  broadcast.kafka.address: ***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092"}, {"servicename": "strategy-flink-order-push-confirm-rate", "namespace": "application", "line": "行号:5  route.kafka.address: ***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092"}, {"servicename": "strategy-flink-order-push-confirm-rate", "namespace": "application", "line": "行号:8  sink.kafka.address: ***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092"}, {"servicename": "driver-task-service", "namespace": "application.yml", "line": "行号:31        host: 127.0.0.1"}, {"servicename": "node-bff-resource", "namespace": "application.yml", "line": "行号:7    url: mongodb://pgm_user:xwUyoblHrLD4k6fDzJwl@**********:8635,**********:8635/t3_fe"}, {"servicename": "gpt-mall", "namespace": "gpt-mall.yaml", "line": "行号:2  open_api_base: http://**********:3000/v1"}, {"servicename": "gpt-mall", "namespace": "gpt-mall.yaml", "line": "行号:8    host: **********"}, {"servicename": "gpt-mall", "namespace": "gpt-mall.yaml", "line": "行号:12    host: ***********"}, {"servicename": "gpt-iqa", "namespace": "application.yaml", "line": "行号:2  open_api_base: http://**********:3000/v1"}, {"servicename": "gpt-iqa", "namespace": "application.yaml", "line": "行号:10    host: **********"}, {"servicename": "gpt-iqa", "namespace": "application.yaml", "line": "行号:14    host: ***********"}, {"servicename": "gpt-iqa", "namespace": "application.yaml", "line": "行号:17    host: **********"}, {"servicename": "gpt-driver-recruit", "namespace": "gpt-driver-recruit.yaml", "line": "行号:3    host: **********"}, {"servicename": "gpt-driver-recruit", "namespace": "gpt-driver-recruit.yaml", "line": "行号:8    host: **********"}, {"servicename": "gpt-driver-recruit", "namespace": "gpt-driver-recruit.yaml", "line": "行号:15    host: ***********"}, {"servicename": "gpt-driver-recruit", "namespace": "gpt-driver-recruit.yaml", "line": "行号:22    api_base: http://**********:3000/v1"}, {"servicename": "digital-api", "namespace": "application.yml", "line": "行号:63      # Redis数据库索引（默认为0  ************"}, {"servicename": "digital-api", "namespace": "application.yml", "line": "行号:82        host: 127.0.0.1"}, {"servicename": "recruit-rpa", "namespace": "application.yml", "line": "行号:136    url: http://**********:12345/driver-recruitment/llm-chat/chat-with-openai"}, {"servicename": "recruit-rpa", "namespace": "application.yml", "line": "行号:137    synUrl: http://**********:12345/driver-recruitment/llm-chat/simple-chat"}, {"servicename": "recruit-rpa", "namespace": "application.yml", "line": "行号:138    syncUrl: http://**********:9999/rpa-message/forward"}, {"servicename": "recruit-rpa", "namespace": "application.yml", "line": "行号:139    confirmMsg: http://**********:12345/driver-recruitment/llm-chat/response-callback"}, {"servicename": "driver-flink-bill", "namespace": "application", "line": "行号:4  t3.doris.address: ***********:8030"}, {"servicename": "driver-flink-bill", "namespace": "application", "line": "行号:6  t3.doris.jdbc.url: jdbc:mysql://**********:9030"}, {"servicename": "driver-flink-bill", "namespace": "application", "line": "行号:10  t3.driver.biz.kafka.server: **********:9092;**********:9092;**********:9092"}, {"servicename": "driver-flink-bill", "namespace": "application", "line": "行号:13  t3.driver.biz.mq.server: **********"}, {"servicename": "driver-flink-bill", "namespace": "application", "line": "行号:15  t3.driver.lingqu.kafka.server: **********:9095"}, {"servicename": "driver-flink-bill", "namespace": "application", "line": "行号:21  t3.redis.host: **********"}, {"servicename": "apm-metric-store-proxy", "namespace": "application.yml", "line": "行号:13          bootstrap-servers: **********:9092,**********:9092,********:9092 #kafka-pre-mixed"}, {"servicename": "apm-metric-store-proxy", "namespace": "application.yml", "line": "行号:26          bootstrap-servers: **********:9092,**********:9092,********:9092 #kafka-pre-mixed"}, {"servicename": "apm-metric-store-proxy", "namespace": "application.yml", "line": "行号:39          bootstrap-servers: **********:9092,**********:9092,********:9092"}, {"servicename": "apm-metric-store-proxy", "namespace": "application.yml", "line": "行号:52          bootstrap-servers: **********:9092,**********:9092,********:9092"}, {"servicename": "apm-metric-store-proxy", "namespace": "application.yml", "line": "行号:65          bootstrap-servers: **********:9092,**********:9092,********:9092"}, {"servicename": "gpt-app", "namespace": "dcapp-gpt-app.yaml", "line": "行号:3    host: ***********"}, {"servicename": "gpt-app", "namespace": "dcapp-gpt-app.yaml", "line": "行号:7    get_driver_id_by_phone_no: http://**********/partner-part/get_driver_id_by_phone_no"}, {"servicename": "gpt-app", "namespace": "dcapp-gpt-app.yaml", "line": "行号:8    get_driver_task_list: http://**********/partner-part/get_driver_task_list"}, {"servicename": "gpt-app", "namespace": "dcapp-gpt-app.yaml", "line": "行号:9    get_driver_task_detail: http://**********/partner-part/get_driver_task_detail"}, {"servicename": "gpt-app", "namespace": "dcapp-gpt-app.yaml", "line": "行号:10    get_driver_task_all_detail: http://**********/partner-part/get_driver_task_all_detail"}, {"servicename": "gpt-app", "namespace": "dcapp-gpt-app.yaml", "line": "行号:11    get_driver_task_rule: http://**********/partner-part/get_driver_task_rule"}, {"servicename": "gpt-app", "namespace": "dcapp-gpt-app.yaml", "line": "行号:12    get_driver_task_online: http://**********/partner-part/get_driver_task_online"}, {"servicename": "gpt-app", "namespace": "dcapp-gpt-app.yaml", "line": "行号:13    get_task_basic_info: http://**********/partner-part/get_task_basic_info"}, {"servicename": "gpt-app", "namespace": "dcapp-gpt-app.yaml", "line": "行号:14    get_driver_list_by_city: http://**********/partner-part/get_driver_list_by_city"}, {"servicename": "gpt-app", "namespace": "dcapp-gpt-app.yaml", "line": "行号:15    get_driver_list_by_taskname: http://**********/partner-part/get_driver_list_by_taskname"}, {"servicename": "gpt-app", "namespace": "dcapp-gpt-app.yaml", "line": "行号:18    host: **********"}, {"servicename": "gpt-app", "namespace": "dcapp-gpt-app.yaml", "line": "行号:22  open_api_base: http://**********:3000/v1"}, {"servicename": "gpt-app", "namespace": "dcapp-gpt-app.yaml", "line": "行号:24  kafka_servers: **********:9092,*********:9092,**********:9092"}, {"servicename": "autodriver-app-api", "namespace": "application.yml", "line": "行号:55        host: 127.0.0.1"}, {"servicename": "push-notification", "namespace": "application.yml", "line": "行号:24      # Redis数据库索引（默认为0  ************"}, {"servicename": "strategy-flink-order-diagnosis", "namespace": "application", "line": "行号:2  bigdata.kafka.server: ***********:9092,**********:9092,***********:9092"}, {"servicename": "strategy-flink-order-diagnosis", "namespace": "application", "line": "行号:3  business.kafka.server: **********:9092,***********:9092,***********:9092"}, {"servicename": "strategy-flink-supply-calc", "namespace": "application", "line": "行号:2  kafka.sink.indicator.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-supply-calc", "namespace": "application", "line": "行号:4  kafka.supplyOnlineStatus.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "ccp-autodrive-alarm", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "strategy-flink-city-cruising-rate", "namespace": "application", "line": "行号:5  hw.bigdata.kafka.server: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-city-cruising-rate", "namespace": "application", "line": "行号:9  result.kafka.server: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-surprise-driver-online-time", "namespace": "application", "line": "行号:2  bigdata.kafka.server: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-surprise-driver-online-time", "namespace": "application", "line": "行号:3  business.kafka.server: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-driver-off-rate-calc", "namespace": "application", "line": "行号:2  bigdata.kafka.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-driver-off-rate-calc", "namespace": "application", "line": "行号:3  business.kafka.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-driver-off-rate-calc", "namespace": "application", "line": "行号:5  city.gird.url: http://**********:80/strategy-biz-api/driverOffRate/getOfflineValueByGridIds"}, {"servicename": "strategy-flink-driver-off-rate-calc", "namespace": "application", "line": "行号:11  driver.location.url: http://**********:80/strategy-biz-api/driverOffRate/getVehicleLocation"}, {"servicename": "strategy-flink-driver-off-rate-calc", "namespace": "application", "line": "行号:14  driver.off.rate.url: http://***********/seldon/mountain-service-test/driver-offline-idm-mining-idm/api/v1.0/predictions"}, {"servicename": "strategy-flink-driver-off-rate-calc", "namespace": "application", "line": "行号:16  driver.offline.url: http://**********:80/strategy-biz-api/driverOffRate/getOfflineDriverInfo"}, {"servicename": "strategy-flink-driver-off-rate-calc", "namespace": "application", "line": "行号:21  route.kafka.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "magic-box-manage-service", "namespace": "application.yml", "line": "行号:57      address: zookeeper://zk-registry-1-c-t3.t3go.com.cn:2181?backup=zk-registry-2-c-t3.t3go.com.cn:2181,zk-registry-3-c-t3.t3go.com.cn:2181&zk.session.expire=20000 #zookeeper://**********:2181?backup=*********:2181,**********:2181"}, {"servicename": "magic-box-core-api", "namespace": "application.yml", "line": "行号:7        host: 127.0.0.1"}, {"servicename": "sp-dcp-settle", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "sp-dcp-settle", "namespace": "application.yml", "line": "行号:42          bootstrap-servers: ***********:9092"}, {"servicename": "sp-dcp-settle", "namespace": "application.yml", "line": "行号:130      #url: http://***********:80"}, {"servicename": "sp-admin-api", "namespace": "application.yml", "line": "行号:9        host: 127.0.0.1"}, {"servicename": "strategy-flink-sync-order-limit", "namespace": "application", "line": "行号:4  route.kafka.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-bubbling-call-rate-predict", "namespace": "application", "line": "行号:4  bubblingCallRateUrl: '{\"320100_7\":\"http://***********/seldon/mountain-service-test/jj-model-bidding-intelligent-marketing/api/v1.0/predictions\"}'"}, {"servicename": "strategy-flink-bubbling-call-rate-predict", "namespace": "application", "line": "行号:5  bubblingCallSinkKafkaAddress: *********:9092,**********:9092,*********:9092"}, {"servicename": "strategy-flink-bubbling-call-rate-predict", "namespace": "application", "line": "行号:7  bubblingKafkaAddress: **********:9092,**********:9092,**********:9092"}, {"servicename": "traffic-copy-audit-log", "namespace": "application.yml", "line": "行号:7        host: 127.0.0.1"}, {"servicename": "traffic-copy-audit-log", "namespace": "application.yml", "line": "行号:38        url: ********************************,*********:8123,*********:8123/t3_traffic"}, {"servicename": "traffic-copy-audit-log", "namespace": "application.yml", "line": "行号:55      bootstrap-servers: **********:9092,**********:9092,**********:9092"}, {"servicename": "bigdata-dri-paxr-rt", "namespace": "application", "line": "行号:2  bigdata.kafka.server: **********:9092,**********:9092,**********:9092"}, {"servicename": "bigdata-dri-paxr-rt", "namespace": "application", "line": "行号:10  dri.recruit.topic.server: **********:9092,**********:9092,**********:9092"}, {"servicename": "bigdata-dri-paxr-rt", "namespace": "application", "line": "行号:11  hbase.zookeeper.quorum: **********:2181,********:2121,*********:2181"}, {"servicename": "bigdata-dri-paxr-rt", "namespace": "bigdata-dri-paxr-rt.yml", "line": "行号:2  bigdata.kafka.server: **********:9092,**********:9092,**********:9092"}, {"servicename": "bigdata-dri-paxr-rt", "namespace": "bigdata-dri-paxr-rt.yml", "line": "行号:3  hbase.zookeeper.quorum: **********:2181,********:2121,*********:2181"}, {"servicename": "bigdata-dri-paxr-rt", "namespace": "bigdata-dri-paxr-rt.yml", "line": "行号:7  dri.recruit.topic.server: ***********:9092,***********:9092,***********:9092"}, {"servicename": "strategy-flink-give-oreder-no-car", "namespace": "application", "line": "行号:2  kafka.route.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "strategy-flink-give-oreder-no-car", "namespace": "application", "line": "行号:5  kafka.sink.orderIndex.address: **********:9092,**********:9092,**********:9092"}, {"servicename": "ccp-data-open-platform", "namespace": "application.yml", "line": "行号:11        host: 127.0.0.1"}], "message": "成功"}